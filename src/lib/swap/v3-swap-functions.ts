import {
  createWalletClient,
  createPublicClient,
  http,
  parseUnits,
  formatUnits,
  type WalletClient,
  type PublicClient,
  type Hash
} from 'viem';
import { HYPERSWAP_CONSTANTS, type SwapParams } from '@/constants/hyperswap';
import { ROUTER_V3_ABI } from './abi/router-v3';
import { ERC20_ABI } from './erc20-abi';

// Create clients
export const createHyperSwapClients = (privateKey?: `0x${string}`) => {
  const publicClient = createPublicClient({
    chain: HYPERSWAP_CONSTANTS.HYPER_CHAIN,
    transport: http(HYPERSWAP_CONSTANTS.RPC_URL),
  });

  let walletClient: WalletClient | null = null;

  if (privateKey) {
    walletClient = createWalletClient({
      chain: HYPERSWAP_CONSTANTS.HYPER_CHAIN,
      transport: http(HYPERSWAP_CONSTANTS.RPC_URL),
    });
  }

  return { publicClient, walletClient };
};

// Check token allowance
export const checkTokenAllowance = async (
  publicClient: PublicClient,
  tokenAddress: `0x${string}`,
  owner: `0x${string}`,
  spender: `0x${string}`
): Promise<bigint> => {
  try {
    const allowance = await publicClient.readContract({
      address: tokenAddress,
      abi: ERC20_ABI,
      functionName: 'allowance',
      args: [owner, spender],
    });
    return allowance as bigint;
  } catch (error) {
    console.error('Error checking allowance:', error);
    return BigInt(0);
  }
};

// Approve token spending
export const approveToken = async (
  walletClient: WalletClient,
  tokenAddress: `0x${string}`,
  spender: `0x${string}`,
  amount: bigint
): Promise<Hash> => {
  const hash = await walletClient.writeContract({
    address: tokenAddress,
    abi: ERC20_ABI,
    functionName: 'approve',
    args: [spender, amount],
  });
  return hash;
};

// Get token balance
export const getTokenBalance = async (
  publicClient: PublicClient,
  tokenAddress: `0x${string}`,
  account: `0x${string}`
): Promise<bigint> => {
  try {
    const balance = await publicClient.readContract({
      address: tokenAddress,
      abi: ERC20_ABI,
      functionName: 'balanceOf',
      args: [account],
    });
    return balance as bigint;
  } catch (error) {
    console.error('Error getting balance:', error);
    return BigInt(0);
  }
};

// Execute exact input single swap
export const exactInputSingleSwap = async (
  walletClient: WalletClient,
  publicClient: PublicClient,
  swapParams: SwapParams,
  account: `0x${string}`
): Promise<Hash> => {
  try {
    // Check allowance first
    const allowance = await checkTokenAllowance(
      publicClient,
      swapParams.tokenIn,
      account,
      HYPERSWAP_CONSTANTS.ROUTER_V3_ADDRESS
    );

    // Approve if needed
    if (allowance < swapParams.amountIn) {
      console.log('Approving token...');
      const approveHash = await approveToken(
        walletClient,
        swapParams.tokenIn,
        HYPERSWAP_CONSTANTS.ROUTER_V3_ADDRESS,
        swapParams.amountIn
      );

      // Wait for approval transaction
      await publicClient.waitForTransactionReceipt({ hash: approveHash });
      console.log('Token approved');
    }

    // Prepare swap parameters
    const deadline = BigInt(Math.floor(Date.now() / 1000) + 1800); // 30 minutes from now
    const fee = swapParams.fee || 3000; // Default 0.3% fee

    const exactInputSingleParams = {
      tokenIn: swapParams.tokenIn,
      tokenOut: swapParams.tokenOut,
      fee,
      recipient: swapParams.recipient,
      deadline,
      amountIn: swapParams.amountIn,
      amountOutMinimum: swapParams.amountOutMinimum,
      sqrtPriceLimitX96: BigInt(0), // No price limit
    };

    // Execute swap
    console.log('Executing swap...');
    const hash = await walletClient.writeContract({
      address: HYPERSWAP_CONSTANTS.ROUTER_V3_ADDRESS,
      abi: ROUTER_V3_ABI,
      functionName: 'exactInputSingle',
      args: [exactInputSingleParams],
    });

    return hash;
  } catch (error) {
    console.error('Swap failed:', error);
    throw error;
  }
};

// Utility function to format token amount
export const formatTokenAmount = (amount: bigint, decimals: number): string => {
  return formatUnits(amount, decimals);
};

// Utility function to parse token amount
export const parseTokenAmount = (amount: string, decimals: number): bigint => {
  return parseUnits(amount, decimals);
};

// Check if token is supported by checking if it's a valid ERC20 token
export const checkTokenSupport = async (
  publicClient: PublicClient,
  tokenAddress: `0x${string}`
): Promise<{ isSupported: boolean; symbol?: string; decimals?: number; name?: string }> => {
  try {
    // Try to read basic ERC20 properties
    const [symbol, decimals, name] = await Promise.all([
      publicClient.readContract({
        address: tokenAddress,
        abi: ERC20_ABI,
        functionName: 'symbol',
      }),
      publicClient.readContract({
        address: tokenAddress,
        abi: ERC20_ABI,
        functionName: 'decimals',
      }),
      publicClient.readContract({
        address: tokenAddress,
        abi: ERC20_ABI,
        functionName: 'name',
      }),
    ]);

    return {
      isSupported: true,
      symbol: symbol as string,
      decimals: decimals as number,
      name: name as string,
    };
  } catch (error) {
    console.error('Token not supported or invalid:', error);
    return {
      isSupported: false,
    };
  }
};

// Check if a trading pair exists by trying to get a quote
export const checkTradingPairSupport = async (
  tokenInAddress: string,
  tokenOutAddress: string
): Promise<boolean> => {
  try {
    const quote = await getSwapQuote(tokenInAddress, tokenOutAddress, '1', 18, 18);
    return quote !== null;
  } catch (error) {
    console.error('Trading pair not supported:', error);
    return false;
  }
};

// Get swap quote from GeckoTerminal
export const getSwapQuote = async (
  tokenInAddress: string,
  tokenOutAddress: string,
  amountIn: string,
  tokenInDecimals: number,
  tokenOutDecimals: number
): Promise<{ amountOut: string; priceImpact: number } | null> => {
  try {
    // Fetch token prices from GeckoTerminal
    const response = await fetch(
      `https://api.geckoterminal.com/api/v2/simple/networks/hyperevm/token_price/${tokenInAddress},${tokenOutAddress}`,
      {
        method: 'GET',
        headers: {
          Accept: 'application/json;version=20230302',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch prices: ${response.status}`);
    }

    const data = await response.json();
    const prices = data.data?.attributes?.token_prices;

    if (!prices || !prices[tokenInAddress] || !prices[tokenOutAddress]) {
      return null;
    }

    const tokenInPrice = parseFloat(prices[tokenInAddress]);
    const tokenOutPrice = parseFloat(prices[tokenOutAddress]);

    if (tokenInPrice <= 0 || tokenOutPrice <= 0) {
      return null;
    }

    // Calculate amount out based on price ratio
    const amountInFloat = parseFloat(amountIn);
    const exchangeRate = tokenInPrice / tokenOutPrice;
    const amountOutFloat = amountInFloat * exchangeRate;

    // Apply a small fee/slippage for more realistic quote (0.3% fee)
    const feeMultiplier = 0.997; // 0.3% fee
    const amountOutWithFee = amountOutFloat * feeMultiplier;

    return {
      amountOut: amountOutWithFee.toFixed(6),
      priceImpact: 0.3 // Simplified price impact
    };
  } catch (error) {
    console.error('Error getting swap quote:', error);
    return null;
  }
};
