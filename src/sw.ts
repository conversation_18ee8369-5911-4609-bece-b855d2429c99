import { cleanupOutdatedCaches, precacheAndRoute } from 'workbox-precaching';
import { clientsClaim } from 'workbox-core';


declare const self: ServiceWorkerGlobalScope;
precacheAndRoute(self.__WB_MANIFEST);

cleanupOutdatedCaches();
self.skipWaiting();
clientsClaim();

// Định nghĩa interface cho dữ liệu notification
interface NotificationData {
  title: string;
  body: string;
  icon: string;
  data?: {
    url?: string;
  };
  tag?: string;
  badge?: string;
  requireInteraction?: boolean;
}

self.addEventListener('push', event => {
  console.log('Push event received:', event);

  // Khởi tạo đối tượng notification mặc định
  let notificationData: NotificationData = {
    title: 'Default Notification',
    body: 'This is a default message',
    icon: '/icon.png',
    data: {} // Thêm thuộc tính data mặc định là một object rỗng
  };

  // Parse dữ liệu từ push event nếu có
  if (event.data) {
    try {
      // Thử parse JSON
      const pushData = JSON.parse(event.data.text());
      console.log('Push data:', pushData);

      // Gán lại thuộc tính từ dữ liệu nhận được
      notificationData = {
        ...notificationData,
        ...pushData
      };
    } catch (e) {
      // Nếu không phải JSON, sử dụng text gốc làm nội dung thông báo
      console.error('Error parsing push data:', e);
      notificationData.body = event.data.text();
    }
  }

  // Hiển thị notification
  event.waitUntil(
    self.registration.showNotification(notificationData.title, {
      body: notificationData.body,
      icon: '/apple-touch-icon.png',
      badge: '/favicon.svg',
      data: {
        dateOfArrival: Date.now(),
        primaryKey: '2',
      },
    })
  );
});

self.addEventListener('notificationclick', function (event) {
  event.notification.close();
  event.waitUntil(clients.openWindow('https://purro.xyz'));
});