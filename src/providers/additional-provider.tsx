import React from 'react';
import { DynamicUserProfile, useDynamicContext } from '@dynamic-labs/sdk-react-core';
import LoadingScreen from '@/components/screens/loading-screen';
import { InstallPrompt } from '@/components/common/install-prompt';
import { PullToRefresh } from '@/components/common/pull-to-refresh';
import useHyperliquidConnection from '@/hooks/useHyperliquidSocket';

const AdditionalProvider = ({ children }: { children: React.ReactNode }) => {
  const { sdkHasLoaded } = useDynamicContext();
  useHyperliquidConnection('mainnet', {
    autoConnect: true
  });

  if (!sdkHasLoaded) {
    return <LoadingScreen />;
  }

  return (
    <>
      <PullToRefresh />
      <InstallPrompt />
      {children}
      <DynamicUserProfile />
    </>
  );
};

export default AdditionalProvider;
