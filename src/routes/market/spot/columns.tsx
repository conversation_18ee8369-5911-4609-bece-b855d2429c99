import { Button } from '@/components/ui/button';
import { cn, formatNumber, formatPercentage } from '@/lib/utils';

import { ColumnDef } from '@tanstack/react-table';
import { ArrowDown, ArrowUp, TrendingDown, TrendingUp } from 'lucide-react';
import { GeckoTerminalPoolResponse } from '@/types/geckoTerminal';
import { getSpotTokenImage } from '@/utils/hyperliquidUtils';
import { getGeckoTerminalName } from '@/utils/formatters';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const toggleThreeStateSorting = (column: any) => {
  const currentSorting = column.getIsSorted();
  if (currentSorting === false) {
    column.toggleSorting(false); // Sort ascending
  } else if (currentSorting === 'asc') {
    column.toggleSorting(true); // Sort descending
  } else {
    column.clearSorting(); // Clear sorting
  }
};

export function createColumns(): ColumnDef<GeckoTerminalPoolResponse>[] {
  return [
    {
      accessorKey: 'attributes.name',
      header: 'Token',
      cell: ({ row }) => {
        const name = getGeckoTerminalName(row.original.attributes.name);
        const icon = getSpotTokenImage(name);
        return (
          <div className="flex items-center gap-1">
            <span className="bg-accent text-muted-foreground rounded p-1 text-xs font-medium">
              #{row.index + 1}
            </span>
            <img
              src={icon}
              alt={name}
              className="h-6 w-6"
              onError={e => {
                e.currentTarget.style.display = 'none';
                const parent = e.currentTarget.parentElement;
                if (parent) {
                  const fallbackDiv = document.createElement('div');
                  fallbackDiv.className =
                    'size-6 bg-primary/10 flex items-center justify-center font-bold text-primary';
                  fallbackDiv.textContent = name.charAt(0).toUpperCase();
                  parent.insertBefore(fallbackDiv, e.currentTarget);
                }
              }}
            />
            <span className="font-medium">{name}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'attributes.base_token_price_usd',
      header: ({ column }) => {
        return (
          <div className="text-left px-2">
            <Button
              variant="ghost"
              onClick={() => toggleThreeStateSorting(column)}
              className="flex w-full items-center justify-end rounded-none !p-0 gap-1"
            >
              Price
              {column.getIsSorted() === 'asc' && <ArrowDown className="size-4" />}
              {column.getIsSorted() === 'desc' && <ArrowUp className="size-4" />}
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const price = parseFloat(row.original.attributes.base_token_price_usd);
        const precision = price < 0.01 ? 6 : 2;
        return (
          <div className="text-right font-medium mx-2">{formatNumber(price, precision, precision)}</div>
        );
      },
    },
    {
      accessorKey: 'attributes.market_cap_usd',
      header: ({ column }) => {
        return (
          <div className="text-left">
            <Button
              variant="ghost"
              onClick={() => toggleThreeStateSorting(column)}
              className="flex w-full items-center justify-end rounded-none !p-0 gap-1"
            >
              Market Cap
              {column.getIsSorted() === 'asc' && <ArrowDown className="size-4" />}
              {column.getIsSorted() === 'desc' && <ArrowUp className="size-4" />}
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const marketCap = row.original.attributes.market_cap_usd;
        return (
          <div className="text-right font-medium">{marketCap ? formatNumber(marketCap) : '-'}</div>
        );
      },
    }, {
      id: '1hChange',
      accessorFn: row => parseFloat(row.attributes.price_change_percentage.h1),
      header: ({ column }) => {
        return (
          <div className="text-left">
            <Button
              variant="ghost"
              onClick={() => toggleThreeStateSorting(column)}
              className="flex w-full items-center justify-end rounded-none !p-0 gap-1"
            >
              1H
              {column.getIsSorted() === 'asc' && <ArrowDown className="size-4" />}
              {column.getIsSorted() === 'desc' && <ArrowUp className="size-4" />}
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const change = parseFloat(row.original.attributes.price_change_percentage.h1);
        const isUp = change > 0;
        const isDown = change < 0;
        const Icon = isUp ? TrendingUp : isDown ? TrendingDown : null;

        return (
          <div className="flex w-full justify-end">
            <div
              className={cn(
                `bg-muted text-muted-foreground flex w-fit items-center justify-end rounded-3xl px-2 py-1 text-left`,
                {
                  'bg-green-500/10 text-green-500': isUp,
                  'bg-red-500/10 text-red-500': isDown,
                }
              )}
            >
              {Icon && <Icon className="mr-1 h-4 w-4" />}
              <span className="font-medium">
                {formatPercentage(row.original.attributes.price_change_percentage.h1)}{' '}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      id: '6hChange',
      accessorFn: row => parseFloat(row.attributes.price_change_percentage.h6),
      header: ({ column }) => {
        return (
          <div className="text-left">
            <Button
              variant="ghost"
              onClick={() => toggleThreeStateSorting(column)}
              className="flex w-full items-center justify-end rounded-none !p-0 gap-1"
            >
              6H
              {column.getIsSorted() === 'asc' && <ArrowDown className="size-4" />}
              {column.getIsSorted() === 'desc' && <ArrowUp className="size-4" />}
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const change = parseFloat(row.original.attributes.price_change_percentage.h6);
        const isUp = change > 0;
        const isDown = change < 0;
        const Icon = isUp ? TrendingUp : isDown ? TrendingDown : null;

        return (
          <div className="flex w-full justify-end">
            <div
              className={cn(
                `bg-muted text-muted-foreground flex w-fit items-center justify-end rounded-3xl px-2 py-1 text-left`,
                {
                  'bg-green-500/10 text-green-500': isUp,
                  'bg-red-500/10 text-red-500': isDown,
                }
              )}
            >
              {Icon && <Icon className="mr-1 h-4 w-4" />}
              <span className="font-medium">
                {formatPercentage(row.original.attributes.price_change_percentage.m5)}{' '}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      id: '24hChange',
      accessorFn: row => parseFloat(row.attributes.price_change_percentage.h24),
      header: ({ column }) => {
        return (
          <div className="text-left">
            <Button
              variant="ghost"
              onClick={() => toggleThreeStateSorting(column)}
              className="flex w-full items-center justify-end rounded-none !p-0 gap-1"
            >
              24H
              {column.getIsSorted() === 'asc' && <ArrowDown className="size-4" />}
              {column.getIsSorted() === 'desc' && <ArrowUp className="size-4" />}
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const change = parseFloat(row.original.attributes.price_change_percentage.h24);
        const isUp = change > 0;
        const isDown = change < 0;
        const Icon = isUp ? TrendingUp : isDown ? TrendingDown : null;

        return (
          <div className="flex w-full justify-end">
            <div
              className={cn(
                `bg-muted text-muted-foreground flex w-fit items-center justify-end rounded-3xl px-2 py-1 text-left`,
                {
                  'bg-green-500/10 text-green-500': isUp,
                  'bg-red-500/10 text-red-500': isDown,
                }
              )}
            >
              {Icon && <Icon className="mr-1 h-4 w-4" />}
              <span className="font-medium">
                {formatPercentage(row.original.attributes.price_change_percentage.h24)}{' '}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      id: 'volume',
      accessorFn: row => parseFloat(row.attributes.volume_usd.h24),
      header: ({ column }) => {
        return (
          <div className="text-left">
            <Button
              variant="ghost"
              onClick={() => toggleThreeStateSorting(column)}
              className="flex w-full items-center justify-end rounded-none !p-0 gap-1 "
            >
              Volume
              {column.getIsSorted() === 'asc' && <ArrowDown className="size-4" />}
              {column.getIsSorted() === 'desc' && <ArrowUp className="size-4" />}
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const volume = row.original.attributes.volume_usd.h24;
        return <div className="text-right font-medium">{formatNumber(volume)}</div>;
      },
    },

    {
      id: 'buySellRatio',
      accessorFn: row => {
        const stats = row.attributes.transactions.h24;
        return stats ? stats.buys / (stats.sells || 1) : 0;
      },
      header: () => <p className="pr-2 text-center">Transactions</p>,
      cell: ({ row }) => {
        const stats = row.original.attributes.transactions.h24;
        const buys = stats ? stats.buys : 0;
        const sells = stats ? stats.sells : 0;
        const percentage = Math.min(100, (buys / (buys + sells || 1)) * 100);

        return (
          <div className="flex flex-col items-center">
            <p className="text-xs">{buys + sells}</p>
            <div className="mb-1 flex items-center space-x-1">
              <span className="text-xs font-medium text-green-500"> {buys} </span>
              <span className="text-muted-foreground text-xs"> /</span>
              <span className="text-xs font-medium text-red-500"> {sells} </span>
            </div>
            <div className="h-2 w-full rounded-full bg-red-500/70">
              <div
                className="h-2 rounded-full bg-green-500/70"
                style={{ width: `${percentage}%` }}
              />
            </div>
          </div>
        );
      },
    },
  ];
}
