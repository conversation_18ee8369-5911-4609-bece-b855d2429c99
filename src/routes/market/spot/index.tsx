import { createColumns } from './columns';
import { useState, useEffect } from 'react';
import { DataTable } from '@/components/data-table';
import { useGeckoTerminal } from '@/hooks/useHyperliquidMarket';
import { useMediaQuery } from 'react-responsive';
import { useNavigate } from 'react-router';
import Pagination from '@/components/pagination';
import TableLoading from '@/components/table-loading';
import TableNoData from '@/components/table-nodata';

const SpotScreen = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const { data, isLoading } = useGeckoTerminal().useTrendingPools('hyperliquid', page);
  const dataResponse = data?.data;
  const [isLastPage, setIsLastPage] = useState(false);
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const isTablet = useMediaQuery({ minWidth: 769, maxWidth: 1024 });


  // Check if next page has data
  const { data: nextPageData } = useGeckoTerminal().useTrendingPools('hyperliquid', page + 1);

  // Update isLastPage when nextPageData changes
  useEffect(() => {
    if (nextPageData?.data && nextPageData.data.length === 0) {
      setIsLastPage(true);
    } else {
      setIsLastPage(false);
    }
  }, [nextPageData]);

  const handlePreviousPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (!isLastPage) {
      setPage(page + 1);
    }
  };

  // Get all columns
  const allColumns = createColumns();

  // Filter columns for mobile - keep only Token, Price, and Change columns
  // Filter columns based on device type
  let columns;
  if (isMobile) {
    // Mobile: show only Token, Price, and 24H Change columns
    columns = allColumns.filter((_, index) => index === 0 || index === 1 || index === 5);
  } else if (isTablet) {
    // Tablet: show Token, Price, Market Cap, 24H Change, and Volume
    columns = allColumns.filter((_, index) => index === 0 || index === 1 || index === 2 || index === 5 || index === 6);
  } else {
    // Desktop: show all columns
    columns = allColumns;
  }

  if (isLoading) return <TableLoading />;
  if (!dataResponse) return <TableNoData />;

  const handleNavigate = (tokenId: string) => {
    navigate(`/market/spot/${tokenId}`);
  };

  return (
    <div className="pb-28 w-full">
      <DataTable columns={columns} data={dataResponse} onNavigate={handleNavigate} />
      <div className="flex items-center justify-center md:justify-center space-y-3 py-4 sm:space-y-0 sm:space-x-2">
        <Pagination
          currentPage={page}
          goToNextPage={handleNextPage}
          goToPrevPage={handlePreviousPage}
          disabled={isLastPage}
        />
      </div>
    </div>

  );
};

export default SpotScreen;
