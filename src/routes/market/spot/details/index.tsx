import React, { useState } from 'react';
import { useParams } from 'react-router';
import useSpotAssetContext from '@/hooks/useSpotAssetContext';
import { formatCurrency } from '@/utils/formatters';
import { cn } from '@/lib/utils';
import { ChevronLeftIcon } from 'lucide-react';
import { useNavigate } from 'react-router';
import { getSpotTokenImage } from '@/utils/hyperliquidUtils';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from 'recharts';
import useHyperliquidApi from '@/hooks/useHyperliquidApi';
import TimeframeSelector from './timeframe-selector';

// Define candle data type
interface CandleData {
    t: number;  // timestamp
    T: number;  // end timestamp
    s: string;  // symbol
    i: string;  // interval
    o: string;  // open
    c: string;  // close
    h: string;  // high
    l: string;  // low
    v: string;  // volume
    n: number;  // number of trades
}

// Define chart data type
interface ChartDataPoint {
    time: string;
    price: number;
    timestamp: number;
    open: number;
    high: number;
    low: number;
    volume: number;
}



// Chart config
const chartConfig = {
    price: {
        label: "Price",
        color: "#088b88",
    },
} satisfies ChartConfig;



const SpotDetailsScreen = () => {
    const navigate = useNavigate();
    const params = useParams();
    const symbol = params.symbol;
    const { spotIndexer } = useSpotAssetContext();
    const { useCandleSnapshot } = useHyperliquidApi();
    const [timeFrame, setTimeFrame] = useState<'1h' | '24h' | '1w' | '1m' | 'ytd' | 'all'>('24h');

    // Get spot info if symbol exists
    const spotInfo = symbol ? spotIndexer?.getSpotInfo([symbol]) : null;

    // Use the hook at the top level with proper conditional enabling
    const universe = symbol && spotInfo && spotInfo[symbol] ? spotInfo[symbol].universe : '';
    const { data: candleData } = useCandleSnapshot(universe || '', timeFrame);

    // Calculate chart domain for better visualization
    const chartDomain = React.useMemo(() => {
        if (!candleData || candleData.length === 0) return [0, 0];
        
        // Find min and max prices
        let minPrice = Infinity;
        let maxPrice = -Infinity;
        
        candleData.forEach((candle: CandleData) => {
            const close = parseFloat(candle.c);
            if (close < minPrice) minPrice = close;
            if (close > maxPrice) maxPrice = close;
        });
        
        // Calculate a narrower range to make the chart more dynamic
        // Use a small percentage of the price range as padding
        const range = maxPrice - minPrice;
        const padding = range * 0.1; // 10% padding
        
        return [
            minPrice - padding, // min domain with padding
            maxPrice + padding  // max domain with padding
        ];
    }, [candleData]);
    
    // Define formatCandleData inside useMemo to avoid dependency issues
    const chartData = React.useMemo(() => {
        const formatData = (data: CandleData[] | undefined): ChartDataPoint[] => {
            if (!data || data.length === 0) return [];

            return data.map(candle => {
                // Format timestamp based on the selected timeframe
                const date = new Date(candle.t);
                let formattedTime = '';

                // Format time based on timeframe
                switch (timeFrame) {
                    case '1h': {
                        // For 1h, show minutes (HH:MM)
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        formattedTime = `${hours}:${minutes}`;
                        break;
                    }
                    case '24h': {
                        // For 24h, show hours (HH:MM)
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        formattedTime = `${hours}:${minutes}`;
                        break;
                    }
                    case '1w': {
                        // For 1w, show day of week
                        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                        formattedTime = days[date.getDay()];
                        break;
                    }
                    case '1m':
                    case 'ytd': {
                        // For 1m and ytd, show MM/DD
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const day = date.getDate().toString().padStart(2, '0');
                        formattedTime = `${month}/${day}`;
                        break;
                    }
                    case 'all': {
                        // For all, show MM/YY
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const year = date.getFullYear().toString().substring(2);
                        formattedTime = `${month}/${year}`;
                        break;
                    }
                    default: {
                        // Default format MM/DD
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const day = date.getDate().toString().padStart(2, '0');
                        formattedTime = `${month}/${day}`;
                    }
                }

                return {
                    time: formattedTime,
                    price: parseFloat(candle.c), // Close price
                    timestamp: candle.t,
                    open: parseFloat(candle.o),
                    high: parseFloat(candle.h),
                    low: parseFloat(candle.l),
                    volume: parseFloat(candle.v)
                };
            });
        };

        return formatData(candleData) || [];
    }, [candleData, timeFrame]);

    // Early return if no symbol
    if (!symbol) return null;

    // const tokenId = spotInfo ? spotInfo[symbol].tokenId : null;
    const priceInfo = spotInfo ? spotInfo[symbol].priceInfo : null;
    if (!priceInfo) return null;
    const percentChange = priceInfo.markPrice / priceInfo.previousDayPrice - 1;
    const formattedPercentChange = percentChange * 100;
    const isUp = formattedPercentChange > 0;
    const isDown = formattedPercentChange < 0;

    return (
        <main>
            <div className="flex items-center gap-4 sticky top-0 left-0 right-0 z-50 border-b border-border backdrop-blur p-2">
                <div className="flex items-center gap-2">
                    <button className="py-2 px-2" onClick={() => navigate(-1)}>
                        <ChevronLeftIcon />
                    </button>
                    <div className="flex items-center gap-2">
                        <img src={getSpotTokenImage(symbol)} alt="" className="w-8 h-8" />
                        <h1 className="text-2xl font-bold">{symbol}</h1>
                    </div>
                </div>
                <div className="flex md:hidden gap-2">
                    <p className="text-xl font-semibold">{formatCurrency(priceInfo.markPrice)}</p>
                    <p className={cn("text-sm font-semibold px-2 py-1 rounded-full", {
                        "text-green-500 bg-green-500/10": isUp,
                        "text-red-500 bg-red-500/10": isDown,
                    })}>{isUp ? '+' : ''}{formattedPercentChange.toFixed(2)}%</p>
                </div>
                <div className="hidden md:flex items-center gap-4">
                    <div>
                        <p className="text-sm font-semibold">Price</p>
                        <p className="text-sm font-semibold">{formatCurrency(priceInfo.markPrice)}</p>
                    </div>
                    <div>
                        <p className="text-sm font-semibold">24h Change</p>
                        <p className={cn("text-sm", {
                            "text-green-500": isUp,
                            "text-red-500": isDown,
                        })}>{isUp ? '+' : ''}{formattedPercentChange.toFixed(2)}%</p>
                    </div>
                    <div>
                        <p className="text-sm font-semibold">24h Volume</p>
                        <p className="text-sm font-semibold">{formatCurrency(priceInfo.dayVolume)}</p>
                    </div>
                </div>
            </div>
            <div className='grid grid-cols-4'>
                <div className="col-span-3 border-r border-b">
                    <div className="p-2">
                        <TimeframeSelector
                            timeFrame={timeFrame}
                            onChange={setTimeFrame}
                        />
                    </div>
                    <div className="h-[60vh] w-full border-t">
                        <ChartContainer config={chartConfig} className="h-full area-chart-wrapper">
                            <AreaChart
                                accessibilityLayer
                                data={chartData}
                                margin={{
                                    left: 0,
                                    right: 0,
                                    top: 0,
                                    bottom: 0,
                                }}
                                className='w-full h-full'
                            >
                                <CartesianGrid stroke="transparent" />
                                <XAxis
                                    dataKey="time"
                                    tickLine={false}
                                    axisLine={false}
                                    tick={false}
                                    height={0}
                                    interval="preserveStartEnd"
                                />

                                <ChartTooltip
                                    cursor={{ stroke: 'var(--border)', strokeWidth: 1 }}
                                    content={<ChartTooltipContent
                                        indicator="dot"
                                        labelFormatter={(value) => `${value}`}
                                        formatter={(value) => [
                                            `$${Number(value).toLocaleString()}`
                                        ]}
                                    />}
                                />
                                <Area
                                    dataKey="price"
                                    type="monotone"
                                    fill="transparent"
                                    fillOpacity={0}
                                    stroke="var(--color-price)"
                                    strokeWidth={2}
                                    yAxisId="price"
                                />
                                <YAxis 
                                    yAxisId="price"
                                    domain={chartDomain}
                                    hide
                                />
                            </AreaChart>
                        </ChartContainer>
                    </div>
                </div>

            </div>
        </main>
    )
}

export default SpotDetailsScreen