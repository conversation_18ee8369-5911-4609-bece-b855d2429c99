import { BellIcon, Book, <PERSON>Key, Paintbrush, RotateCwIcon, <PERSON>roll } from "lucide-react"
import packageJson from '../../../package.json';
import { Link } from "react-router";
import HeaderBackBtn from "@/components/navigation/header-back-btn";

const MobileSettingsScreen = () => {

    return (
        <main className="h-screen">
            <HeaderBackBtn title="Settings" />

            <div className="space-y-2 pt-3 px-2">
                <div className="bg-primary rounded-2xl" onClick={() => {
                    window.location.reload();
                }}>
                    <div className="flex items-center gap-3 px-3 py-2">
                        <RotateCwIcon className="size-5 text-white" />
                        <p className="text-base text-white">Refresh the App</p>
                    </div>
                </div>
                <div className="bg-card rounded-2xl">
                    <Link to="/mobile-settings/notifications" className="flex items-center gap-3 px-3 py-2 border-b border-border">
                        <BellIcon className="size-5" />
                        <p className="text-base">Notifications</p>
                    </Link>
                    <div className="flex items-center gap-3 px-3 py-2">
                        <Paintbrush className="size-5" />
                        <p className="text-base">Appearance</p>
                    </div>
                </div>
                <div className="bg-card rounded-2xl">
                    <a href="https://docs.purro.xyz" className="flex items-center gap-3 px-3 py-2 border-b border-border">
                        <Book className="size-5" />
                        <p className="text-base">Documentation</p>
                    </a>
                    <Link to="/terms" className="flex items-center gap-3 px-3 py-2 border-b border-border">
                        <Scroll className="size-5" />
                        <p className="text-base">Terms of Service</p>
                    </Link>
                    <Link to="/privacy" className="flex items-center gap-3 px-3 py-2">
                        <FileKey className="size-5" />
                        <p className="text-base">Privacy Policy</p>
                    </Link>
                </div>
            </div>
            <div className="text-muted-foreground mt-4 text-center text-xs">v{packageJson.version}</div>
        </main>
    )
}

export default MobileSettingsScreen