import { Routes, Route } from 'react-router';
import { AuthLayout, LoginLayout, MarketLayout, WalletLayout } from '@/layouts';
import LoginScreen from './login';
import ExploreScreen from './explore';
import SpotScreen from './market/spot';
import HyperEvmLayout from './market/hyperevm/hyperevm-layout';
import Settings from './settings';
import Terms from './terms';
import Privacy from './privacy';
import Mobile from './mobile';
import FuturesScreen from './futures';
import MobileSettingsScreen from './mobile-settings';
import SpotTradeScreen from './trade/spot';
import Trade from './trade';
import HyperEvmDexesScreen from './market/hyperevm/hyperevm-dexes';
import WalletScreen from './wallet';
import MarketRedirect from './market/market-redirect';
import SpotDetailsScreen from './market/spot/details';
import Notifications from './mobile-settings/notifications';

export default function AppRoutes() {
  return (
    <Routes>
      <Route element={<LoginLayout />}>
        <Route path="/login" element={<LoginScreen />} />
      </Route>
      <Route element={<AuthLayout />}>
        <Route path="/" element={<WalletLayout />} >
          <Route index element={<WalletScreen />} />
        </Route>
        <Route path="/market" element={<MarketLayout />}>
          <Route index element={<MarketRedirect />} />
          <Route path="spot" element={<SpotScreen />} />
          <Route path="hyperevm" element={<HyperEvmLayout />} >
            <Route index element={<HyperEvmDexesScreen />} />
          </Route>
        </Route>
        <Route path="/market/spot/:symbol" element={<SpotDetailsScreen />} />
        <Route path="/trade" element={<Trade />} />
        <Route path="/trade/spot/:tokenId" element={<SpotTradeScreen />} />
        <Route path="/explore" element={<ExploreScreen />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/futures" element={<FuturesScreen />} />
      </Route>
      <Route path="/mobile-settings" element={<MobileSettingsScreen />} />
      <Route path="/mobile-settings/notifications" element={<Notifications />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />
      <Route path="/mobile" element={<Mobile />} />
    </Routes>
  );
}
