import { type FC, useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowDownUp, Loader2, ExternalLink } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
import { useSwap } from '@/hooks/useSwap';
import { getSwapTokens, type SwapToken } from '@/constants/hyperswap';
import TokenSelector from '@/components/TokenSelector';

const SwapBox: FC = () => {
  const { setShowAuthFlow } = useDynamicContext();
  const {
    swapState,
    tokenBalances,
    swapQuote,
    executeSwap,
    getBalance,
    getQuote,
    isConnected,
    walletAddress
  } = useSwap();

  // Initialize with default tokens
  const defaultTokens = getSwapTokens();
  const [tokenIn, setTokenIn] = useState<SwapToken>(defaultTokens[0] || {
    symbol: 'WETH',
    name: 'Wrapped Ethereum',
    address: '******************************************',
    decimals: 18,
    icon: 'Ξ'
  });
  const [tokenOut, setTokenOut] = useState<SwapToken>(defaultTokens[1] || {
    symbol: 'USDC',
    name: 'USD Coin',
    address: '******************************************',
    decimals: 6,
    icon: '💵'
  });
  const [amountIn, setAmountIn] = useState('');
  const [skipValidation, setSkipValidation] = useState(false);
  const slippage = 0.5; // Fixed slippage for now

  // Load balances when wallet connects or tokens change
  useEffect(() => {
    if (isConnected && walletAddress) {
      getBalance(tokenIn, walletAddress);
      getBalance(tokenOut, walletAddress);
    }
  }, [isConnected, walletAddress, tokenIn, tokenOut, getBalance]);

  // Get quote when amount or tokens change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (amountIn && parseFloat(amountIn) > 0) {
        getQuote(tokenIn, tokenOut, amountIn);
      }
    }, 500); // Debounce for 500ms

    return () => clearTimeout(timeoutId);
  }, [amountIn, tokenIn, tokenOut, getQuote]);

  const handleSwapTokens = () => {
    const temp = tokenIn;
    setTokenIn(tokenOut);
    setTokenOut(temp);
    setAmountIn('');
    // Quote will be automatically updated by useEffect
  };

  const handleExecuteSwap = async () => {
    if (!amountIn || !isConnected) return;

    await executeSwap(tokenIn, tokenOut, amountIn, slippage, skipValidation);
  };

  const handleConnectWallet = () => {
    setShowAuthFlow(true);
  };

  const getTokenBalance = (token: SwapToken) => {
    return tokenBalances[token.symbol]?.formatted || '0';
  };

  const isSwapDisabled = !amountIn || !isConnected || swapState.isLoading || parseFloat(amountIn) <= 0;

  return (
    <div className="w-full max-w-md mx-auto bg-card rounded-xl p-6 shadow-lg">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Swap</h3>
          {swapState.isSuccess && swapState.txHash && (
            <a
              href={`https://explorer.hyperliquid.xyz/tx/${swapState.txHash}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1 text-sm text-green-600 hover:text-green-700"
            >
              <ExternalLink className="h-3 w-3" />
              View Tx
            </a>
          )}
        </div>

        {/* Development Options */}
        <div className="flex items-center justify-between p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex flex-col">
            <span className="text-sm font-medium">Skip Validation</span>
            <span className="text-xs text-muted-foreground">For testing - bypasses trading pair checks</span>
          </div>
          <Switch
            checked={skipValidation}
            onCheckedChange={setSkipValidation}
          />
        </div>

        {/* Error Message */}
        {swapState.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{swapState.error}</p>
          </div>
        )}

        {/* Token In */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">From</label>
            {isConnected && (
              <span className="text-xs text-muted-foreground">
                Balance: {getTokenBalance(tokenIn)}
              </span>
            )}
          </div>
          <div className="flex gap-2">
            <TokenSelector
              selectedToken={tokenIn}
              onTokenSelect={setTokenIn}
              otherToken={tokenOut}
              disabled={swapState.isLoading}
            />
            <Input
              type="number"
              placeholder="0.0"
              value={amountIn}
              onChange={(e) => setAmountIn(e.target.value)}
              className="flex-1"
              disabled={swapState.isLoading}
            />
          </div>
        </div>

        {/* Swap Button */}
        <div className="flex justify-center">
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            onClick={handleSwapTokens}
            disabled={swapState.isLoading}
          >
            <ArrowDownUp className="h-4 w-4" />
          </Button>
        </div>

        {/* Token Out */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">To</label>
            {isConnected && (
              <span className="text-xs text-muted-foreground">
                Balance: {getTokenBalance(tokenOut)}
              </span>
            )}
          </div>
          <div className="flex gap-2">
            <TokenSelector
              selectedToken={tokenOut}
              onTokenSelect={setTokenOut}
              otherToken={tokenIn}
              disabled={swapState.isLoading}
            />
            <Input
              type="number"
              placeholder="0.0"
              value={swapQuote.isLoading ? 'Loading...' : swapQuote.amountOut}
              readOnly
              className="flex-1 bg-muted"
            />
          </div>
        </div>

        {/* Quote Info */}
        {swapQuote.amountOut && !swapQuote.isLoading && amountIn && (
          <div className="p-3 bg-muted/50 rounded-lg space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Exchange Rate</span>
              <span>
                1 {tokenIn.symbol} = {(parseFloat(swapQuote.amountOut) / parseFloat(amountIn)).toFixed(6)} {tokenOut.symbol}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Price Impact</span>
              <span className={swapQuote.priceImpact > 1 ? 'text-yellow-600' : 'text-green-600'}>
                {swapQuote.priceImpact.toFixed(2)}%
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Slippage Tolerance</span>
              <span>{slippage}%</span>
            </div>
          </div>
        )}

        {/* Swap Execute Button */}
        {!isConnected ? (
          <Button
            className="w-full"
            size="lg"
            onClick={handleConnectWallet}
          >
            Connect Wallet
          </Button>
        ) : (
          <Button
            className="w-full"
            size="lg"
            onClick={handleExecuteSwap}
            disabled={isSwapDisabled}
          >
            {swapState.isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Swapping...
              </div>
            ) : (
              'Swap'
            )}
          </Button>
        )}

        {/* Success Message */}
        {swapState.isSuccess && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-600">Swap completed successfully!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SwapBox;