import { type FC, useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowDownUp, Loader2, ExternalLink } from 'lucide-react';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
import { useSwap } from '@/hooks/useSwap';
import { SWAP_TOKENS, type SwapToken } from '@/constants/hyperswap';

const SwapBox: FC = () => {
  const { setShowAuthFlow } = useDynamicContext();
  const {
    swapState,
    tokenBalances,
    swapQuote,
    executeSwap,
    getBalance,
    getQuote,
    isConnected,
    walletAddress
  } = useSwap();

  const [tokenIn, setTokenIn] = useState<SwapToken>(SWAP_TOKENS[0]);
  const [tokenOut, setTokenOut] = useState<SwapToken>(SWAP_TOKENS[1]);
  const [amountIn, setAmountIn] = useState('');
  const slippage = 0.5; // Fixed slippage for now

  // Load balances when wallet connects or tokens change
  useEffect(() => {
    if (isConnected && walletAddress) {
      getBalance(tokenIn, walletAddress);
      getBalance(tokenOut, walletAddress);
    }
  }, [isConnected, walletAddress, tokenIn, tokenOut, getBalance]);

  // Get quote when amount or tokens change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (amountIn && parseFloat(amountIn) > 0) {
        getQuote(tokenIn, tokenOut, amountIn);
      }
    }, 500); // Debounce for 500ms

    return () => clearTimeout(timeoutId);
  }, [amountIn, tokenIn, tokenOut, getQuote]);

  const handleSwapTokens = () => {
    const temp = tokenIn;
    setTokenIn(tokenOut);
    setTokenOut(temp);
    setAmountIn('');
    // Quote will be automatically updated by useEffect
  };

  const handleExecuteSwap = async () => {
    if (!amountIn || !isConnected) return;

    await executeSwap(tokenIn, tokenOut, amountIn, slippage);
  };

  const handleConnectWallet = () => {
    setShowAuthFlow(true);
  };

  const getTokenBalance = (token: SwapToken) => {
    return tokenBalances[token.symbol]?.formatted || '0';
  };

  const isSwapDisabled = !amountIn || !isConnected || swapState.isLoading || parseFloat(amountIn) <= 0;

  return (
    <div className="w-full max-w-md mx-auto bg-card rounded-xl p-6 shadow-lg">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Swap</h3>
          {swapState.isSuccess && swapState.txHash && (
            <a
              href={`https://explorer.hyperliquid.xyz/tx/${swapState.txHash}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1 text-sm text-green-600 hover:text-green-700"
            >
              <ExternalLink className="h-3 w-3" />
              View Tx
            </a>
          )}
        </div>

        {/* Error Message */}
        {swapState.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{swapState.error}</p>
          </div>
        )}

        {/* Token In */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">From</label>
            {isConnected && (
              <span className="text-xs text-muted-foreground">
                Balance: {getTokenBalance(tokenIn)}
              </span>
            )}
          </div>
          <div className="flex gap-2">
            <Select
              value={tokenIn.symbol}
              onValueChange={(value: string) => setTokenIn(SWAP_TOKENS.find(t => t.symbol === value) || SWAP_TOKENS[0])}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue>
                  <div className="flex items-center gap-2">
                    <span>{tokenIn.icon}</span>
                    <span>{tokenIn.symbol}</span>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {SWAP_TOKENS.map((token) => (
                  <SelectItem key={token.symbol} value={token.symbol}>
                    <div className="flex items-center gap-2">
                      <span>{token.icon}</span>
                      <span>{token.symbol}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="0.0"
              value={amountIn}
              onChange={(e) => setAmountIn(e.target.value)}
              className="flex-1"
              disabled={swapState.isLoading}
            />
          </div>
        </div>

        {/* Swap Button */}
        <div className="flex justify-center">
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            onClick={handleSwapTokens}
            disabled={swapState.isLoading}
          >
            <ArrowDownUp className="h-4 w-4" />
          </Button>
        </div>

        {/* Token Out */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">To</label>
            {isConnected && (
              <span className="text-xs text-muted-foreground">
                Balance: {getTokenBalance(tokenOut)}
              </span>
            )}
          </div>
          <div className="flex gap-2">
            <Select
              value={tokenOut.symbol}
              onValueChange={(value: string) => setTokenOut(SWAP_TOKENS.find(t => t.symbol === value) || SWAP_TOKENS[1])}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue>
                  <div className="flex items-center gap-2">
                    <span>{tokenOut.icon}</span>
                    <span>{tokenOut.symbol}</span>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {SWAP_TOKENS.map((token) => (
                  <SelectItem key={token.symbol} value={token.symbol}>
                    <div className="flex items-center gap-2">
                      <span>{token.icon}</span>
                      <span>{token.symbol}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="0.0"
              value={swapQuote.isLoading ? 'Loading...' : swapQuote.amountOut}
              readOnly
              className="flex-1 bg-muted"
            />
          </div>
        </div>

        {/* Quote Info */}
        {swapQuote.amountOut && !swapQuote.isLoading && amountIn && (
          <div className="p-3 bg-muted/50 rounded-lg space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Exchange Rate</span>
              <span>
                1 {tokenIn.symbol} = {(parseFloat(swapQuote.amountOut) / parseFloat(amountIn)).toFixed(6)} {tokenOut.symbol}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Price Impact</span>
              <span className={swapQuote.priceImpact > 1 ? 'text-yellow-600' : 'text-green-600'}>
                {swapQuote.priceImpact.toFixed(2)}%
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Slippage Tolerance</span>
              <span>{slippage}%</span>
            </div>
          </div>
        )}

        {/* Swap Execute Button */}
        {!isConnected ? (
          <Button
            className="w-full"
            size="lg"
            onClick={handleConnectWallet}
          >
            Connect Wallet
          </Button>
        ) : (
          <Button
            className="w-full"
            size="lg"
            onClick={handleExecuteSwap}
            disabled={isSwapDisabled}
          >
            {swapState.isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Swapping...
              </div>
            ) : (
              'Swap'
            )}
          </Button>
        )}

        {/* Success Message */}
        {swapState.isSuccess && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-600">Swap completed successfully!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SwapBox;