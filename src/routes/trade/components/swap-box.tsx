import { type FC, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowDownUp } from 'lucide-react';

interface Token {
  symbol: string;
  name: string;
  icon: string;
}

const TOKENS: Token[] = [
  { symbol: 'HYPE', name: 'Hyperswap', icon: '🔄' },
  { symbol: 'USDC', name: 'USD Coin', icon: '💵' },
  { symbol: 'ETH', name: 'Ethereum', icon: 'Ξ' },
  { symbol: 'BTC', name: 'Bitcoin', icon: '₿' },
];

const SwapBox: FC = () => {
  const [tokenIn, setTokenIn] = useState<Token>(TOKENS[0]);
  const [tokenOut, setTokenOut] = useState<Token>(TOKENS[1]);
  const [amountIn, setAmountIn] = useState('');

  const handleSwap = () => {
    const temp = tokenIn;
    setTokenIn(tokenOut);
    setTokenOut(temp);
  };

  return (
    <div className="w-full max-w-md mx-auto bg-card rounded-xl p-6 shadow-lg">
      <div className="space-y-4">
        {/* Token In */}
        <div className="space-y-2">
          <label className="text-sm font-medium">From</label>
          <div className="flex gap-2">
            <Select value={tokenIn.symbol} onValueChange={(value: string) => setTokenIn(TOKENS.find(t => t.symbol === value) || TOKENS[0])}>
              <SelectTrigger className="w-[120px]">
                <SelectValue>
                  <div className="flex items-center gap-2">
                    <span>{tokenIn.icon}</span>
                    <span>{tokenIn.symbol}</span>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {TOKENS.map((token) => (
                  <SelectItem key={token.symbol} value={token.symbol}>
                    <div className="flex items-center gap-2">
                      <span>{token.icon}</span>
                      <span>{token.symbol}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="0.0"
              value={amountIn}
              onChange={(e) => setAmountIn(e.target.value)}
              className="flex-1"
            />
          </div>
        </div>

        {/* Swap Button */}
        <div className="flex justify-center">
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            onClick={handleSwap}
          >
            <ArrowDownUp className="h-4 w-4" />
          </Button>
        </div>

        {/* Token Out */}
        <div className="space-y-2">
          <label className="text-sm font-medium">To</label>
          <div className="flex gap-2">
            <Select value={tokenOut.symbol} onValueChange={(value: string) => setTokenOut(TOKENS.find(t => t.symbol === value) || TOKENS[1])}>
              <SelectTrigger className="w-[120px]">
                <SelectValue>
                  <div className="flex items-center gap-2">
                    <span>{tokenOut.icon}</span>
                    <span>{tokenOut.symbol}</span>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {TOKENS.map((token) => (
                  <SelectItem key={token.symbol} value={token.symbol}>
                    <div className="flex items-center gap-2">
                      <span>{token.icon}</span>
                      <span>{token.symbol}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="0.0"
              value={amountIn ? (Number(amountIn) * 1.5).toFixed(6) : ''}
              readOnly
              className="flex-1"
            />
          </div>
        </div>

        {/* Swap Button */}
        <Button className="w-full" size="lg">
          Swap
        </Button>
      </div>
    </div>
  );
};

export default SwapBox; 