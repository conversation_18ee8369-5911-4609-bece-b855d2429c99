import { useParams } from 'react-router';
import useSpotAssetContext from '@/hooks/useSpotAssetContext';
import { formatCurrency } from '@/utils/formatters';

const SpotTradeScreen = () => {
  const params = useParams<{ tokenId: string }>();
  const { spotIndexer } = useSpotAssetContext(1000);

  if (!params.tokenId) return null;
  const spotInfo = spotIndexer?.getSpotInfo([params.tokenId])
  const tokenId = spotInfo ? spotInfo[params.tokenId].tokenId : null;

  console.log(spotInfo)

  return <div className="w-full h-full">
    <div className="w-full bg-card m-1 rounded">
      {spotInfo && <div className="flex space-x-2 w-full">
        <p className="text-2xl font-semibold">{spotInfo[params.tokenId].symbol}</p>
        <p className="text-2xl font-semibold">{spotInfo[params.tokenId].price}</p>
        <p className="text-2xl font-semibold">{spotInfo[params.tokenId].priceInfo?.markPrice}</p>
        <p className="text-2xl font-semibold">{spotInfo[params.tokenId].priceInfo?.midPrice}</p>
        <p className="text-2xl font-semibold">{spotInfo[params.tokenId].priceInfo?.previousDayPrice}</p>
        <p className="text-2xl font-semibold">{formatCurrency(spotInfo[params.tokenId].priceInfo?.dayVolume)}</p>
      </div>}

    </div>
    <div className="grid grid-cols-1 lg:grid-cols-4 h-[60vh]">
      <div className="md:col-span-4 m-1 rounded overflow-hidden">
        {tokenId && <iframe className="rounded overflow-hidden" height="100%" width="100%" id="geckoterminal-embed" title="GeckoTerminal Embed" src={`https://www.geckoterminal.com/hyperliquid/pools/${tokenId}?embed=1&info=0&swaps=0&grayscale=0&light_chart=0&chart_type=price&resolution=15m`} allow="clipboard-write" allowFullScreen />}
      </div>

    </div>

  </div>;
};

export default SpotTradeScreen;
