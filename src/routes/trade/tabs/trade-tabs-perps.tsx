import { type FC } from 'react';
import { useNavigate } from 'react-router';

const TradePerpList: FC = () => {
  const navigate = useNavigate();

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Example perpetual pairs */}
        {['BTC-PERP', 'ETH-PERP', 'HYPE-PERP'].map((pair) => (
          <div
            key={pair}
            className="bg-card p-4 rounded-lg hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => navigate(`/trade/perpetuals/${pair}`)}
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">{pair}</h3>
                <p className="text-muted-foreground">Last price: $0.00</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">24h Change</p>
                <p className="text-green-500">+0.00%</p>
              </div>
            </div>
            <div className="mt-2 flex justify-between text-sm text-muted-foreground">
              <span>Funding Rate: 0.00%</span>
              <span>Open Interest: $0.00</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TradePerpList; 