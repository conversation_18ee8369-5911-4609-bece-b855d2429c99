import { type FC } from 'react';
import { useNavigate } from 'react-router';

const TradeSpotList: FC = () => {
  const navigate = useNavigate();

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Example trading pairs */}
        {['HYPE/USDC', 'BTC/USDC', 'ETH/USDC'].map((pair) => (
          <div
            key={pair}
            className="bg-card p-4 rounded-lg hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => navigate(`/trade/spot/${pair}`)}
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">{pair}</h3>
                <p className="text-muted-foreground">Last price: $0.00</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">24h Change</p>
                <p className="text-green-500">+0.00%</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TradeSpotList; 