import { DynamicEmbeddedWidget } from '@dynamic-labs/sdk-react-core';
import packageJson from '../../../package.json';
import mobile from '@/assets/mobile.png';

const Login = () => {
  return (
    <div className="flex flex-col items-start md:flex-row">
      <div className="w-full md:w-1/2">
        <div className="flex flex-col items-start px-6">
          <h1 className="text-primary pb-2 text-3xl font-bold tracking-tight md:text-4xl">
            Simplicity by design, <br /> everywhere you need it
          </h1>
          <p className="text-muted-foreground">
            Purro Wallet transforms your crypto journey with an intuitive, cross-platform experience
            that puts your Web3 needs first at every touchpoint.
          </p>
        </div>
        <DynamicEmbeddedWidget />
        <div className="text-muted-foreground mt-4 text-center text-xs">v{packageJson.version}</div>
      </div>
      <div className="flex h-[70vh] w-full items-center justify-center md:w-1/2">
        <img src={mobile} alt="" className="h-full w-fit object-contain" />
      </div>
    </div>
  );
};

export default Login;
