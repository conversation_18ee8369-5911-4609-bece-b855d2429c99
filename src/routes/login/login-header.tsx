import { useState } from 'react';

const navItems = [
  { label: 'Privacy & Safety', href: '#' },
  { label: 'Desktop & Mobile', href: '#' },
  { label: 'Documentation', href: '#' },
];

const LoginHeader = () => {
  const [isNavOpen, setIsNavOpen] = useState(false);
  return (
    <div className="sticky top-0 z-50 bg-transparent backdrop-blur">
      <div className="relative w-full">
        <div className="flex items-center justify-between px-6 py-4">
          <img src="/icon-transparent.png" alt="" className="size-10 md:size-12" />
          <nav className="hidden md:block">
            <ul className="flex items-center gap-4">
              {navItems.map((item, index) => (
                <li key={index}>
                  <a href={item.href}>{item.label}</a>
                </li>
              ))}
            </ul>
          </nav>
          <button
            className="cursor-pointer transition-transform duration-300 ease-in-out md:hidden"
            onClick={() => setIsNavOpen(!isNavOpen)}
          >
            {isNavOpen ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6 rotate-90 transition-transform duration-300"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6 transition-transform duration-300"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                />
              </svg>
            )}
          </button>
        </div>
        <div
          className={`bg-background border-border absolute top-[100%] w-full overflow-hidden border-y transition-all duration-300 ease-in-out md:hidden ${isNavOpen ? 'max-h-40 opacity-100' : 'max-h-0 opacity-0'
            }`}
        >
          <ul className="flex flex-col items-start gap-4 p-6">
            {navItems.map((item, index) => (
              <li
                key={index}
                className="transform transition-transform duration-300 ease-in-out hover:translate-x-2"
              >
                <a href={item.href}>{item.label}</a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default LoginHeader;
