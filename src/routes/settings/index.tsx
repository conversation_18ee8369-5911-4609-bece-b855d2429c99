import { Bell, Paintbrush } from 'lucide-react';
import { useState } from 'react';
import Notifications from './notifications';
import Appearance from './appearance';

type Settings = 'notifications' | 'appearance';

const SettingsPageLayout = () => {
  const [setting, setSetting] = useState<Settings>('notifications');

  return (
    <div className="flex h-full flex-col md:flex-row p-2">
      <div className="bg-background border-border sticky top-0 z-10 mb-4 flex justify-start border-b w-50 flex-col border-r">
        <button
          className={`flex items-center justify-start gap-2 px-3 py-2 text-base font-medium ${setting === 'notifications'
            ? 'text-primary border-primary border-r-2'
            : 'text-muted-foreground hover:text-foreground'
            }`}
          onClick={() => setSetting('notifications')}
        >
          <Bell className="size-5" />
          Notifications
        </button>
        <button
          className={`flex items-center justify-start gap-2 px-3 py-2 text-base font-medium ${setting === 'appearance'
            ? 'text-primary border-primary border-r-2'
            : 'text-muted-foreground hover:text-foreground'
            }`}
          onClick={() => setSetting('appearance')}
        >
          <Paintbrush className="size-5" />
          Appearance
        </button>
      </div>
      <div className="flex-1">
        <div className="w-[50%]">
          {setting === 'notifications' && <Notifications />}
          {setting === 'appearance' && <Appearance />}
        </div>
      </div>
    </div>
  );
};

export default SettingsPageLayout;
