import useWalletTabStore from '@/store/walletTab';
import WalletTabsSpot from './tabs/wallet-tabs-spot';
import WalletTabsPerps from './tabs/wallet-tabs-perps';
import WalletTabsEVM from './tabs/wallet-tabs-evm';
import WalletNFTs from './tabs/wallet-nfts/wallet-nfts';

const WalletScreen = () => {
  const { currentTab } = useWalletTabStore();
  return (
    <>
      {
        currentTab === 'spot' && <WalletTabsSpot />
      }
      {
        currentTab === 'perps' && <WalletTabsPerps />
      }
      {
        currentTab === 'evm' && <WalletTabsEVM />
      }
      {
        currentTab === 'nft' && <WalletNFTs />
      }
    </>
  );
};



export default WalletScreen;
