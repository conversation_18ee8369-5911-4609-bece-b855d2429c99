import { useState } from 'react';
import WalletSpotList from './wallet-tabs-spot';
import UserPerpBalanceDisplay from './wallet-tabs-perps';
import UserEvmBalanceDisplay from './wallet-tabs-evm';
import WalletTabsNFTs from './wallet-nfts/wallet-nfts';

const WalletTabs = () => {
  const [activeTab, setActiveTab] = useState<'spot' | 'perpetuals' | 'evm' | 'nfts'>('spot');

  return (
    <div className="relative mt-2">
      <div className="absolute top-0 right-0 left-0">
        <div className="bg-background border-border sticky top-0 z-10 mb-4 flex border-b">
          <button
            className={`px-4 py-2 text-base font-medium ${activeTab === 'spot'
              ? 'text-primary border-primary border-b-2'
              : 'text-muted-foreground hover:text-foreground'
              }`}
            onClick={() => setActiveTab('spot')}
          >
            Spot
          </button>
          <button
            className={`px-4 py-2 text-base font-medium ${activeTab === 'perpetuals'
              ? 'text-primary border-primary border-b-2'
              : 'text-muted-foreground hover:text-foreground'
              }`}
            onClick={() => setActiveTab('perpetuals')}
          >
            Perpetuals
          </button>
          <button
            className={`px-4 py-2 text-base font-medium ${activeTab === 'evm'
              ? 'text-primary border-primary border-b-2'
              : 'text-muted-foreground hover:text-foreground'
              }`}
            onClick={() => setActiveTab('evm')}
          >
            EVM
          </button>
          <button
            className={`px-4 py-2 text-base font-medium ${activeTab === 'nfts'
              ? 'text-primary border-primary border-b-2'
              : 'text-muted-foreground hover:text-foreground'
              }`}
            onClick={() => setActiveTab('nfts')}
          >
            NFTs
          </button>
        </div>

        {/* Tab Content */}
        <div className='pb-28'>
          {activeTab === 'spot' && <WalletSpotList />}
          {activeTab === 'perpetuals' && <UserPerpBalanceDisplay />}
          {activeTab === 'evm' && <UserEvmBalanceDisplay />}
          {activeTab === 'nfts' && <WalletTabsNFTs />}
        </div>
      </div>
    </div>
  );
};

export default WalletTabs;
