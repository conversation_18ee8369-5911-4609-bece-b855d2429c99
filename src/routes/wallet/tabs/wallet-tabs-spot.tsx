import HyperLiquidSpotDataIndexer from '@/classes/HyperliquidSpotDataIndexer';
import { useMemo } from 'react';
import { HyperliquidApiSpotAssetContext } from '@/types/hyperliquidSpot';
import { getSpotTokenImage } from '@/utils/hyperliquidUtils';
import { usePortfolioData } from '@/hooks/usePortfolioData';
import { formatCurrency } from '@/utils/formatters';
import TabsLoading from './tabs-loading';
import TabsError from './tabs-error';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
const WalletTabsSpot = () => {
  const { primaryWallet } = useDynamicContext();
  const address = primaryWallet?.address || "";
  const { spotData, isSpotLoading, spotError } = usePortfolioData(address);

  // Create indexer only when dataContext is available
  const indexer = useMemo(() => {
    if (
      !spotData?.contextData ||
      !Array.isArray(spotData.contextData) ||
      spotData.contextData.length < 2
    ) {
      return null;
    }
    try {
      return new HyperLiquidSpotDataIndexer(spotData.contextData as HyperliquidApiSpotAssetContext);
    } catch (error) {
      console.error('Error creating SpotDataIndexer:', error);
      return null;
    }
  }, [spotData?.contextData]);

  // Process user balances only when both data sources are available
  const userBalances = useMemo(() => {
    if (!indexer || !spotData?.balanceData) {
      return [];
    }
    // Get user balances and sort by market value from highest to lowest
    const balances = indexer.processUserBalances(spotData.balanceData);
    return balances.sort((a, b) => {
      const valueA = a.marketValue || 0;
      const valueB = b.marketValue || 0;
      return valueB - valueA; // Sort descending (highest to lowest)
    });
  }, [indexer, spotData?.balanceData]);

  // Calculate portfolio value using the indexer
  const portfolioValue = useMemo(() => {
    if (!userBalances.length) return 0;
    return indexer?.getPortfolioValue(userBalances) || 0;
  }, [userBalances, indexer]);

  // Handle loading state
  if (isSpotLoading) {
    return <TabsLoading />;
  }

  if (spotError) {
    return <TabsError message="Failed to load spot data" />;
  }

  return (
    <div className="space-y-6 px-2">
      {/* Account Summary */}
      <div className="mb-4">
        <div className="grid grid-cols-2 gap-2 lg:grid-cols-4">
          <div className="bg-card rounded-lg p-3">
            <div className="text-muted-foreground text-sm">Spot Balance</div>
            <div className="font-semibold text-lg">{formatCurrency(portfolioValue)}</div>
          </div>
          <div className="bg-card rounded-lg p-3">
            <div className="text-muted-foreground text-sm">Assets</div>
            <div className="font-semibold text-lg">{userBalances.length}</div>
          </div>
        </div>
      </div>

      {/* Asset List */}
      <>
        {userBalances.length > 0 ? (
          <div className="space-y-2 overflow-visible">
            {userBalances.map((balance, index) => (
              <div key={index} className="bg-card rounded-lg p-3">
                <div className="mb-2 flex justify-between">
                  <div className="min-w-0 flex-1 overflow-hidden">
                    <div className="flex items-center gap-2">
                      <img
                        src={getSpotTokenImage(balance.coin)}
                        alt={balance.coin}
                        className="size-10 rounded-full"
                        onError={e => {
                          e.currentTarget.style.display = 'none';
                          const parent = e.currentTarget.parentElement;
                          if (parent) {
                            const fallbackDiv = document.createElement('div');
                            fallbackDiv.className =
                              'size-10 bg-primary/10 rounded-full flex items-center justify-center font-bold text-primary';
                            fallbackDiv.textContent = balance.coin.charAt(0).toUpperCase();
                            parent.insertBefore(fallbackDiv, e.currentTarget);
                          }
                        }}
                      />
                      <div>
                        <div className="truncate font-semibold text-lg">{balance.coin}</div>
                        <div className="text-muted-foreground mt-0.5 truncate text-sm">
                          {balance.total.toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 4,
                          })}{' '}
                          {balance.coin}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-lg">{formatCurrency(balance.marketValue)}</div>
                    {indexer && balance.entryNtl > 0 && (
                      <div
                        className={`text-sm ${(indexer.calculatePnL(balance)?.pnl ?? 0) >= 0
                          ? 'text-green-500'
                          : 'text-red-500'
                          }`}
                      >
                        {indexer.calculatePnL(balance)?.pnlPercentage.toFixed(2)}%
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-card text-muted-foreground rounded-lg p-3 text-center">
            No assets found
          </div>
        )}
      </>
    </div>
  );
};

export default WalletTabsSpot;
