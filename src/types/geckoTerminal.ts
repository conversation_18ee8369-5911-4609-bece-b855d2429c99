export interface GeckoTerminalToken {
  id: string;
  type: string;
}

export interface GeckoTerminalTransaction {
  buys: number;
  sells: number;
  buyers: number;
  sellers: number;
}

export interface GeckoTerminalPoolResponse {
  id: string;
  type: string;
  attributes: {
    base_token_price_usd: string;
    base_token_price_native_currency: string;
    quote_token_price_usd: string;
    quote_token_price_native_currency: string;
    base_token_price_quote_token: string;
    quote_token_price_base_token: string;
    address: string;
    name: string;
    pool_created_at: string;
    fdv_usd: string;
    market_cap_usd: string | null;
    price_change_percentage: {
      m5: string;
      m15: string;
      m30: string;
      h1: string;
      h6: string;
      h24: string;
    };
    transactions: {
      m5: GeckoTerminalTransaction;
      m15: GeckoTerminalTransaction;
      m30: GeckoTerminalTransaction;
      h1: GeckoTerminalTransaction;
      h6: GeckoTerminalTransaction;
      h24: GeckoTerminalTransaction;
    };
    volume_usd: {
      m5: string;
      m15: string;
      m30: string;
      h1: string;
      h6: string;
      h24: string;
    };
    reserve_in_usd: string | null;
  };
  relationships: {
    base_token: { data: GeckoTerminalToken };
    quote_token: { data: GeckoTerminalToken };
    dex: { data: { id: string; type: string } };
  };
}

// Type for time frame options
export type TimeFrame = 'm5' | 'm15' | 'm30' | 'h1' | 'h6' | 'h24';
