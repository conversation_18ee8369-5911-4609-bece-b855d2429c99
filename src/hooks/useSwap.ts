import { useState, useCallback } from 'react';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
import { createWalletClient, custom, type Hash } from 'viem';
import {
  exactInputSingleSwap,
  getTokenBalance,
  formatTokenAmount,
  parseTokenAmount,
  createHyperSwapClients,
  getSwapQuote
} from '@/lib/swap/v3-swap-functions';
import { HYPERSWAP_CONSTANTS, type SwapParams, type SwapToken } from '@/constants/hyperswap';

interface SwapState {
  isLoading: boolean;
  error: string | null;
  txHash: Hash | null;
  isSuccess: boolean;
}

interface TokenBalance {
  balance: bigint;
  formatted: string;
}

interface SwapQuote {
  amountOut: string;
  priceImpact: number;
  isLoading: boolean;
}

export const useSwap = () => {
  const { primaryWallet } = useDynamicContext();
  const [swapState, setSwapState] = useState<SwapState>({
    isLoading: false,
    error: null,
    txHash: null,
    isSuccess: false,
  });

  const [tokenBalances, setTokenBalances] = useState<Record<string, TokenBalance>>({});
  const [swapQuote, setSwapQuote] = useState<SwapQuote>({
    amountOut: '',
    priceImpact: 0,
    isLoading: false,
  });

  // Reset swap state
  const resetSwapState = useCallback(() => {
    setSwapState({
      isLoading: false,
      error: null,
      txHash: null,
      isSuccess: false,
    });
  }, []);

  // Get token balance
  const getBalance = useCallback(async (token: SwapToken, address?: `0x${string}`) => {
    if (!address) return null;

    try {
      const { publicClient } = createHyperSwapClients();
      const balance = await getTokenBalance(publicClient, token.address, address);
      const formatted = formatTokenAmount(balance, token.decimals);

      const balanceData = { balance, formatted };
      setTokenBalances(prev => ({
        ...prev,
        [token.symbol]: balanceData
      }));

      return balanceData;
    } catch (error) {
      console.error(`Error getting ${token.symbol} balance:`, error);
      return null;
    }
  }, []);

  // Get swap quote
  const getQuote = useCallback(async (
    tokenIn: SwapToken,
    tokenOut: SwapToken,
    amountIn: string
  ) => {
    if (!amountIn || parseFloat(amountIn) <= 0) {
      setSwapQuote({
        amountOut: '',
        priceImpact: 0,
        isLoading: false,
      });
      return;
    }

    setSwapQuote(prev => ({ ...prev, isLoading: true }));

    try {
      const quote = await getSwapQuote(
        tokenIn.address,
        tokenOut.address,
        amountIn,
        tokenIn.decimals,
        tokenOut.decimals
      );

      if (quote) {
        setSwapQuote({
          amountOut: quote.amountOut,
          priceImpact: quote.priceImpact,
          isLoading: false,
        });
      } else {
        setSwapQuote({
          amountOut: '',
          priceImpact: 0,
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Error getting quote:', error);
      setSwapQuote({
        amountOut: '',
        priceImpact: 0,
        isLoading: false,
      });
    }
  }, []);

  // Execute swap
  const executeSwap = useCallback(async (
    tokenIn: SwapToken,
    tokenOut: SwapToken,
    amountIn: string,
    slippage: number = 0.5
  ) => {
    if (!primaryWallet?.address) {
      setSwapState(prev => ({ ...prev, error: 'Wallet not connected' }));
      return;
    }

    setSwapState({
      isLoading: true,
      error: null,
      txHash: null,
      isSuccess: false,
    });

    try {
      // Check if wallet connector is available
      if (!primaryWallet.connector) {
        throw new Error('Wallet connector not available');
      }

      // Create wallet client with Dynamic wallet
      const walletClient = createWalletClient({
        chain: HYPERSWAP_CONSTANTS.HYPER_CHAIN,
        transport: custom(primaryWallet.connector),
        account: primaryWallet.address as `0x${string}`,
      });

      const { publicClient } = createHyperSwapClients();

      // Parse amount
      const amountInParsed = parseTokenAmount(amountIn, tokenIn.decimals);

      // Calculate minimum amount out with slippage
      // This is a simplified calculation - in production you'd want to get actual price from DEX
      const estimatedAmountOut = amountInParsed; // 1:1 for demo - replace with actual price calculation
      const slippageMultiplier = BigInt(Math.floor((100 - slippage) * 100));
      const amountOutMinimum = (estimatedAmountOut * slippageMultiplier) / BigInt(10000);

      const swapParams: SwapParams = {
        tokenIn: tokenIn.address,
        tokenOut: tokenOut.address,
        recipient: primaryWallet.address as `0x${string}`,
        amountIn: amountInParsed,
        amountOutMinimum,
        fee: 3000, // 0.3%
      };

      const txHash = await exactInputSingleSwap(
        walletClient,
        publicClient,
        swapParams,
        primaryWallet.address as `0x${string}`
      );

      setSwapState({
        isLoading: false,
        error: null,
        txHash,
        isSuccess: true,
      });

      // Refresh balances after successful swap
      setTimeout(() => {
        getBalance(tokenIn, primaryWallet.address as `0x${string}`);
        getBalance(tokenOut, primaryWallet.address as `0x${string}`);
      }, 2000);

    } catch (error: any) {
      console.error('Swap failed:', error);
      setSwapState({
        isLoading: false,
        error: error.message || 'Swap failed',
        txHash: null,
        isSuccess: false,
      });
    }
  }, [primaryWallet, getBalance]);

  return {
    swapState,
    tokenBalances,
    swapQuote,
    executeSwap,
    getBalance,
    getQuote,
    resetSwapState,
    isConnected: !!primaryWallet?.address,
    walletAddress: primaryWallet?.address as `0x${string}` | undefined,
  };
};
