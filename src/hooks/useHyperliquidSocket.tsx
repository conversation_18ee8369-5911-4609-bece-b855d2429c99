// useHyperliquidWebSocket.ts
import { useEffect } from 'react';
import useHyperliquidSocketStore from '@/store/hyperliquidSocket';
import { Network } from '@/types/hyperliquidSocket';

interface UseHyperliquidWebSocketOptions {
    autoConnect?: boolean;
}

/**
 * Hook giúp tự động kết nối tới Hyperliquid WebSocket API
 * và dọn dẹp khi component unmount
 */
const useHyperliquidWebSocket = (
    network: Network = 'mainnet',
    options: UseHyperliquidWebSocketOptions = {}
) => {
    const { autoConnect = true } = options;

    // Lấy state và actions từ store
    const {
        status,
        error,
        allMids,
        setNetwork,
        connect,
        disconnect
    } = useHyperliquidSocketStore();

    // Đặt network
    useEffect(() => {
        setNetwork(network);
    }, [network, setNetwork]);

    // Tự động kết nối khi component mount
    useEffect(() => {
        if (autoConnect) {
            connect();
        }

        // Ngắt kết nối khi component unmount
        return () => {
            disconnect();
        };
    }, [autoConnect, connect, disconnect]);

    return {
        status,
        error,
        allMids,
        connect,
        disconnect
    };
};

export default useHyperliquidWebSocket;