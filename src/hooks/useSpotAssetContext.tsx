import { useQuery } from "@tanstack/react-query";
import { fetchSpotAssetsContext } from "../services/hyperliquidApi";
import QueryKeys from "@/constants/queryKeys";
import { useMemo } from "react";
import HyperLiquidSpotDataIndexer from "@/classes/HyperliquidSpotDataIndexer";
import { HyperliquidApiSpotAssetContext } from "@/types/hyperliquidSpot";

const useSpotAssetContext = (refetchInterval?: number) => {
    const spotContextQuery = useQuery({
        queryKey: [QueryKeys.SPOT_ASSETS],
        queryFn: () => fetchSpotAssetsContext(),
        staleTime: 1 * 1000, // 1 seconds
        refetchInterval: refetchInterval,
    });

    const spotIndexer = useMemo(() => {
        if (!spotContextQuery.data) return null;

        const contextData = spotContextQuery.data;
        if (!contextData || !Array.isArray(contextData) || contextData.length < 2) {
            return null;
        }
        try {
            return new HyperLiquidSpotDataIndexer(contextData as HyperliquidApiSpotAssetContext);
        } catch (error) {
            console.error('Error creating SpotDataIndexer:', error);
            return null;
        }
    }, [spotContextQuery.data]);

    return {
        spotContextQuery,
        spotIndexer,
    };
};

export default useSpotAssetContext;