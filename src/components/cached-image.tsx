import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { ImageOff } from "lucide-react";
import { useEffect, useState } from "react";

interface CachedImageProps {
    src: string;
    alt?: string;
    className?: string;
    loadingFallback?: React.ReactNode;
    errorFallback?: React.ReactNode;
}

// In-memory cache outside of component to persist between renders
const imageCache = new Map<string, string>();

const CachedImage = ({
    src,
    alt = "",
    className = "",
    loadingFallback = <div className={cn("aspect-square bg-gray-200 animate-pulse rounded-md", className)}></div>,
    errorFallback = <div className={cn("aspect-square bg-gray-200 animate-pulse rounded-md flex items-center justify-center", className)}><ImageOff className="size-12 text-red-300" /></div>
}: CachedImageProps) => {
    const [cachedInLocalStorage, setCachedInLocalStorage] = useState(false);

    // Check if image is cached in localStorage
    useEffect(() => {
        const cachedImagesMap = JSON.parse(localStorage.getItem('cachedImages') || '{}');
        if (cachedImagesMap[src]) {
            setCachedInLocalStorage(true);
        }
    }, [src]);

    // Use React Query with proper stale time configuration
    const { data: imageUrl, isLoading, isError } = useQuery({
        queryKey: ['image', src],
        queryFn: async () => {
            // Check in-memory cache first
            if (imageCache.has(src)) {
                return imageCache.get(src);
            }

            // Fetch the image
            try {
                const response = await fetch(src);
                if (!response.ok) throw new Error('Failed to fetch image');

                const blob = await response.blob();
                const objectUrl = URL.createObjectURL(blob);

                // Save to in-memory cache
                imageCache.set(src, objectUrl);

                // Save to localStorage (just mark that it exists)
                const cachedImagesMap = JSON.parse(localStorage.getItem('cachedImages') || '{}');
                cachedImagesMap[src] = true;
                localStorage.setItem('cachedImages', JSON.stringify(cachedImagesMap));

                return objectUrl;
            } catch (error) {
                console.error('Error loading image:', error);
                throw error;
            }
        },
        staleTime: Infinity, // Never consider data stale
        gcTime: Infinity, // Keep in cache forever (renamed from cacheTime in v5)
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        retry: 1,
        enabled: !cachedInLocalStorage, // Only fetch if not already cached
    });

    // Show loading state
    if (isLoading && !cachedInLocalStorage) {
        return <>{loadingFallback}</>;
    }

    if (isError) {
        return <>{errorFallback}</>;
    }

    // Return image with proper URL
    return (
        <img
            src={cachedInLocalStorage ? src : (imageUrl || src)}
            alt={alt}
            className={className}
            onError={(e) => {
                // Handle runtime image loading errors
                e.currentTarget.style.display = 'none';
                setCachedInLocalStorage(false);
                const cachedImagesMap = JSON.parse(localStorage.getItem('cachedImages') || '{}');
                delete cachedImagesMap[src];
                localStorage.setItem('cachedImages', JSON.stringify(cachedImagesMap));
            }}
            loading="lazy"
        />
    );
};

export default CachedImage;