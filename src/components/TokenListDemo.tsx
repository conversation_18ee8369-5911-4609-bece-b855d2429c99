import { useState, useEffect } from 'react';
import { getFeaturedTokens, searchTokens } from '@/services/tokenListService';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

const TokenListDemo = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [tokens, setTokens] = useState<any[]>([]);

  useEffect(() => {
    if (searchQuery.length >= 2) {
      const results = searchTokens(searchQuery, 999);
      setTokens(results.slice(0, 20));
    } else {
      const featured = getFeaturedTokens(999);
      setTokens(featured.slice(0, 20));
    }
  }, [searchQuery]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-card rounded-xl p-6 shadow-lg">
      <h3 className="text-lg font-semibold mb-4">Available Tokens ({tokens.length})</h3>
      
      {/* Search */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search tokens..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>
      
      {/* Token Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-96 overflow-y-auto">
        {tokens.map((token) => (
          <div
            key={token.address}
            className="flex items-center gap-3 p-3 rounded-lg border hover:bg-muted transition-colors"
          >
            {token.logoURI ? (
              <img 
                src={token.logoURI} 
                alt={token.symbol}
                className="w-8 h-8 rounded-full"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling!.style.display = 'flex';
                }}
              />
            ) : null}
            <div 
              className={`w-8 h-8 rounded-full bg-muted flex items-center justify-center text-lg ${
                token.logoURI ? 'hidden' : 'flex'
              }`}
            >
              {getTokenIcon(token.symbol)}
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm">{token.symbol}</div>
              <div className="text-xs text-muted-foreground truncate">
                {token.name}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {tokens.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          {searchQuery ? 'No tokens found' : 'Loading tokens...'}
        </div>
      )}
      
      <div className="mt-4 text-sm text-muted-foreground">
        {searchQuery ? `Search results for "${searchQuery}"` : 'Featured tokens'}
      </div>
    </div>
  );
};

const getTokenIcon = (symbol: string): string => {
  const iconMap: Record<string, string> = {
    'WETH': 'Ξ',
    'ETH': 'Ξ',
    'USDC': '💵',
    'USDT': '💵',
    'USDe': '💵',
    'WHYPE': '🔥',
    'HYPE': '🔥',
    'HFUN': '🎮',
    'OMNIX': '🌐',
    'PURR': '🐱',
    'JEFF': '🤖',
    'BASED': '🔵',
    'DEGEN': '🎩',
    'HIGHER': '⬆️',
    'MFER': '😎',
    'TOSHI': '🐕',
    'BRETT': '🐸',
    'PEPE': '🐸',
    'WIF': '🐕',
    'MOODENG': '🦛',
    'GOAT': '🐐',
    'PNUT': '🥜',
    'CHILLGUY': '😎',
    'FARTCOIN': '💨',
    'ZEREBRO': '🧠',
    'VIRTUAL': '🔮',
    'AI16Z': '🤖',
    'GRIFFAIN': '🦅',
    'LUNA': '🌙',
    'SOLANA': '☀️',
    'BTC': '₿',
    'DOGE': '🐕',
    'SHIB': '🐕',
  };
  
  return iconMap[symbol] || '🪙';
};

export default TokenListDemo;
