import { truncateAddress } from '@/lib/utils';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';

interface Props {
  onClick?: () => void;
}

const OpenUserProfileBtn = ({ onClick }: Props) => {
  const { primaryWallet } = useDynamicContext();

  return (
    <div
      className="from-primary/10 to-primary/10 hover:border-primary/40 flex cursor-pointer items-center gap-2 rounded-xl border bg-gradient-to-r pr-2 transition-all duration-300 hover:shadow-md"
      onClick={onClick}
    >
      <img src="/maskable-icon-512x512.png" alt="" className="h-8 w-8 rounded-full" />
      {primaryWallet?.address && (
        <div className="flex items-center gap-1.5">
          <p className="text-sm font-medium">{truncateAddress(primaryWallet.address)}</p>
        </div>
      )}
    </div>
  );
};

export default OpenUserProfileBtn;
