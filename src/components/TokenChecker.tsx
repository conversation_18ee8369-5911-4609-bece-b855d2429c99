import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { isTokenSupported, isTradingPairSupported, getSupportedTokens } from '@/utils/tokenChecker';

const TokenChecker = () => {
  const [tokenAddress, setTokenAddress] = useState('');
  const [tokenInAddress, setTokenInAddress] = useState('');
  const [tokenOutAddress, setTokenOutAddress] = useState('');
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const checkToken = async () => {
    if (!tokenAddress) return;
    
    setLoading(true);
    try {
      const result = await isTokenSupported(tokenAddress);
      setResults({ type: 'token', data: result });
    } catch (error) {
      setResults({ type: 'error', data: error });
    }
    setLoading(false);
  };

  const checkTradingPair = async () => {
    if (!tokenInAddress || !tokenOutAddress) return;
    
    setLoading(true);
    try {
      const result = await isTradingPairSupported(tokenInAddress, tokenOutAddress);
      setResults({ type: 'pair', data: result });
    } catch (error) {
      setResults({ type: 'error', data: error });
    }
    setLoading(false);
  };

  const getAllSupportedTokens = async () => {
    setLoading(true);
    try {
      const tokens = await getSupportedTokens();
      setResults({ type: 'all', data: tokens });
    } catch (error) {
      setResults({ type: 'error', data: error });
    }
    setLoading(false);
  };

  return (
    <div className="p-6 bg-card rounded-xl shadow-lg space-y-6">
      <h3 className="text-lg font-semibold">Token Support Checker</h3>
      
      {/* Check Single Token */}
      <div className="space-y-2">
        <h4 className="font-medium">Check Token Support</h4>
        <div className="flex gap-2">
          <Input
            placeholder="Token address (0x...)"
            value={tokenAddress}
            onChange={(e) => setTokenAddress(e.target.value)}
            className="flex-1"
          />
          <Button onClick={checkToken} disabled={loading || !tokenAddress}>
            Check Token
          </Button>
        </div>
      </div>

      {/* Check Trading Pair */}
      <div className="space-y-2">
        <h4 className="font-medium">Check Trading Pair Support</h4>
        <div className="flex gap-2">
          <Input
            placeholder="Token In address"
            value={tokenInAddress}
            onChange={(e) => setTokenInAddress(e.target.value)}
            className="flex-1"
          />
          <Input
            placeholder="Token Out address"
            value={tokenOutAddress}
            onChange={(e) => setTokenOutAddress(e.target.value)}
            className="flex-1"
          />
          <Button 
            onClick={checkTradingPair} 
            disabled={loading || !tokenInAddress || !tokenOutAddress}
          >
            Check Pair
          </Button>
        </div>
      </div>

      {/* Get All Supported Tokens */}
      <div className="space-y-2">
        <Button onClick={getAllSupportedTokens} disabled={loading}>
          Get All Supported Tokens
        </Button>
      </div>

      {/* Results */}
      {results && (
        <div className="p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">Results:</h4>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      )}

      {/* Quick Test Buttons */}
      <div className="space-y-2">
        <h4 className="font-medium">Quick Tests</h4>
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setTokenAddress('******************************************');
              setTimeout(checkToken, 100);
            }}
          >
            Test WETH
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setTokenAddress('******************************************');
              setTimeout(checkToken, 100);
            }}
          >
            Test USDC
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setTokenInAddress('******************************************');
              setTokenOutAddress('******************************************');
              setTimeout(checkTradingPair, 100);
            }}
          >
            Test WETH/USDC Pair
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TokenChecker;
