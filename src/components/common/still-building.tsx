import { Construction } from 'lucide-react';

interface StillBuildingProps {
  title?: string;
  description?: string;
  className?: string;
}

export function StillBuilding({
  title = 'Still Building',
  description = 'This feature is currently under development.',
  className,
}: StillBuildingProps) {
  return (
    <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>
      <Construction className="text-primary size-12 mb-4" />
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
}
