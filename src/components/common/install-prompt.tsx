import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

// Define window extensions
interface NavigatorWithStandalone extends Navigator {
  standalone?: boolean;
}

export function InstallPrompt() {
  const [isIOS, setIsIOS] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);
  const [isMobileDevice, setIsMobileDevice] = useState(false);

  useEffect(() => {
    const detectMobile = () => {
      const userAgent = navigator.userAgent || '';

      // iOS detection
      const isIOS =
        /iPad|iPhone|iPod/.test(userAgent) &&
        !(window as unknown as { MSStream: boolean }).MSStream;
      setIsIOS(isIOS);

      // Android detection
      const isAndroid = /Android/.test(userAgent);
      setIsAndroid(isAndroid);

      // General mobile detection
      const isMobile = /iPhone|iPad|iPod|Android|webOS|BlackBerry|IEMobile|Opera Mini/i.test(
        userAgent
      );
      setIsMobileDevice(isMobile);

      // Check if already installed as PWA
      const navigatorWithStandalone = window.navigator as NavigatorWithStandalone;
      const isInStandaloneMode =
        window.matchMedia('(display-mode: standalone)').matches ||
        navigatorWithStandalone.standalone ||
        document.referrer.includes('android-app://');
      setIsStandalone(isInStandaloneMode);
    };

    detectMobile();

    // Show prompt if not installed and on mobile device
    if (!isStandalone && isMobileDevice) {
      const timer = setTimeout(() => {
        setShowPrompt(true);
      }, 2000);
      return () => clearTimeout(timer);
    }

    // Listen for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    const handleChange = (e: MediaQueryListEvent) => {
      setIsStandalone(e.matches);
      if (e.matches) setShowPrompt(false);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [isStandalone, isMobileDevice]);

  const dismissPrompt = () => {
    setShowPrompt(false);
  };

  // Don't render anything if already installed or not a mobile device
  if (isStandalone || !isMobileDevice) {
    return null;
  }

  return (
    <>
      {showPrompt && (
        <div className="bg-background fixed top-0 right-0 left-0 z-[100] w-full border-b shadow-sm">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 flex h-9 w-9 items-center justify-center overflow-hidden rounded-full">
                <img
                  src="/apple-touch-icon.png"
                  alt="Purro logo"
                  width={24}
                  height={24}
                  className="size-8 rounded-full object-cover"
                  onError={e => {
                    e.currentTarget.style.display = 'none';
                    if (e.currentTarget.parentElement) {
                      e.currentTarget.parentElement.innerHTML = '📱';
                    }
                  }}
                />
              </div>
              <div>
                <h3 className="text-base font-semibold">Purro</h3>
                <p className="text-muted-foreground text-sm">Install application</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="default"
                size="sm"
                className="h-8 rounded-full px-4 text-sm"
                onClick={() => setShowInstructions(!showInstructions)}
              >
                Install
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8" onClick={dismissPrompt}>
                <X size={16} />
              </Button>
            </div>
          </div>

          {showInstructions && (
            <div className="border-t p-4">
              {isIOS && (
                <div className="mb-3">
                  <h4 className="mb-1 font-medium">Install on iOS:</h4>
                  <ol className="list-inside list-decimal space-y-1 text-base">
                    <li>Tap the share button at the bottom of your screen</li>
                    <li>Scroll down and tap "Add to Home Screen"</li>
                    <li>Tap "Add" in the top right corner</li>
                  </ol>
                  <p className="text-muted-foreground mt-1 text-sm">
                    Note: This feature is only available in Safari browser
                  </p>
                </div>
              )}

              {isAndroid && (
                <div>
                  <h4 className="mb-1 font-medium">Install on Android:</h4>
                  <ol className="list-inside list-decimal space-y-1 text-base">
                    <li>Tap the menu button (three dots) in the top right</li>
                    <li>Tap "Install app" or "Add to Home Screen"</li>
                    <li>Tap "Install" to confirm</li>
                  </ol>
                  <p className="text-muted-foreground mt-1 text-sm">
                    Note: This feature is only available in Chrome browser
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </>
  );
}
