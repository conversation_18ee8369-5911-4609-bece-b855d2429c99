import { useNavigate } from 'react-router'
import { ChevronLeftIcon } from 'lucide-react'

const HeaderBackBtn = ({ title }: { title: string }) => {
    const navigate = useNavigate();
    return (
        <div className="flex items-center gap-2 sticky top-0 left-0 right-0 z-50 border-b border-border backdrop-blur">
            <button className="py-3 px-2" onClick={() => navigate(-1)}>
                <ChevronLeftIcon />
            </button>
            <h1 className="text-xl font-semibold">{title}</h1>
        </div>
    )
}

export default HeaderBackBtn