'use client';

import { Link } from 'react-router';
import { useLocation } from 'react-router';
import { navItems } from './navItems';
import { cn, isPwa } from '@/lib/utils';

export default function MobileTabsNavigation() {
  const location = useLocation();
  const pathname = location.pathname;
  const isPwaApp = isPwa();
  const isAndroid = /android/i.test(navigator.userAgent);

  // <PERSON><PERSON><PERSON> đ<PERSON>nh padding bottom phù hợp dựa trên nền tảng
  const getPaddingClass = () => {
    if (isPwaApp) {
      if (isAndroid) {
        return 'pb-5'; // Padding cho Android PWA
      }
      return 'pb-safe'; // Padding cho iOS PWA
    }
    return 'pb-3'; // Padding mặc định cho browser
  };

  return (
    <div className="bg-background fixed right-0 bottom-0 left-0 z-40 w-full border-t border-border md:hidden">
      <div className={cn(`grid grid-cols-5 pt-3`, getPaddingClass())}>
        {navItems.map(tab => {
          const isActive = tab.url === '/' ? pathname === '/' : pathname.startsWith(tab.url);

          return (
            <Link
              key={tab.title}
              to={tab.url}
              className={`flex flex-col items-center justify-center ${isActive
                ? 'text-primary dark:text-primary'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
            >
              <tab.icon className="size-6" />
              <span className="mt-1 text-sm">{tab.title}</span>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
