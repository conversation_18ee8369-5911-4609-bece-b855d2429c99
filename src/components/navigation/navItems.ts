import { CandlestickChartIcon, GroupedBarChartIcon, SearchInsideIcon, SwapHorizontalCircleIcon, WalletIcon } from '@/assets/icons';
import { Book, FileKey, LucideIcon, Scroll, SettingsIcon, Smartphone } from 'lucide-react';
import React from 'react';

type NavItem = {
  title: string;
  url: string;
  icon: LucideIcon | React.FC;
};

export const navItems: NavItem[] = [
  {
    title: 'Wallet',
    url: '/',
    icon: WalletIcon,
  },
  {
    title: 'Market',
    url: '/market',
    icon: GroupedBarChartIcon,
  },
  {
    title: 'Trade',
    url: '/trade',
    icon: SwapHorizontalCircleIcon,
  },
  {
    title: "Futures",
    url: "/futures",
    icon: CandlestickChartIcon,
  },
  // {
  //     name: "Earn",
  //     href: "/earn",
  //     icon: Gem,
  // },
  {
    title: 'Explore',
    url: '/explore',
    icon: SearchInsideIcon,
  },
];

export const navSecondaryItems: NavItem[] = [
  {
    title: 'Settings',
    url: '/settings',
    icon: SettingsIcon,
  },
  // {
  //   title: 'Get Help',
  //   url: '#',
  //   icon: HelpCircleIcon,
  // },
  {
    title: 'Documentation',
    url: 'https://docs.purro.xyz',
    icon: Book,
  },
  {
    title: 'Get Mobile App',
    url: '/mobile',
    icon: Smartphone,
  },
  {
    title: 'Terms of Service',
    url: '/terms',
    icon: Scroll,
  },
  {
    title: 'Privacy Policy',
    url: '/privacy',
    icon: FileKey,
  },
];

