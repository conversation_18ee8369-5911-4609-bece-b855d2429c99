import { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ChevronDown, Search } from 'lucide-react';
import { type SwapToken } from '@/constants/hyperswap';
import { searchTokens, getFeaturedTokens } from '@/services/tokenListService';

// Helper function to get token icon (moved outside component)
const getTokenIcon = (symbol: string): string => {
  const iconMap: Record<string, string> = {
    'WETH': 'Ξ',
    'ETH': 'Ξ',
    'USDC': '💵',
    'USDT': '💵',
    'USDe': '💵',
    'WHYPE': '🔥',
    'HYPE': '🔥',
    'HFUN': '🎮',
    'OMNIX': '🌐',
    'PURR': '🐱',
    'JEFF': '🤖',
    'BASED': '🔵',
    'DEGEN': '🎩',
    'HIGHER': '⬆️',
    'MFER': '😎',
    'TOSHI': '🐕',
    'BRETT': '🐸',
    'PEPE': '🐸',
    'WIF': '🐕',
  };

  return iconMap[symbol] || '🪙';
};

interface TokenSelectorProps {
  selectedToken: SwapToken;
  onTokenSelect: (token: SwapToken) => void;
  otherToken?: SwapToken; // To filter out the other selected token
  disabled?: boolean;
}

const TokenSelector = ({ selectedToken, onTokenSelect, otherToken, disabled }: TokenSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Get available tokens
  const availableTokens = useMemo(() => {
    if (searchQuery.length >= 2) {
      return searchTokens(searchQuery, 999).map(token => ({
        symbol: token.symbol,
        name: token.name,
        address: token.address as `0x${string}`,
        decimals: token.decimals,
        logoURI: token.logoURI,
        icon: getTokenIcon(token.symbol),
      }));
    }

    return getFeaturedTokens(999).map(token => ({
      symbol: token.symbol,
      name: token.name,
      address: token.address as `0x${string}`,
      decimals: token.decimals,
      logoURI: token.logoURI,
      icon: getTokenIcon(token.symbol),
    }));
  }, [searchQuery]);

  // Filter out the other selected token
  const filteredTokens = useMemo(() => {
    return availableTokens.filter(token =>
      !otherToken || token.address.toLowerCase() !== otherToken.address.toLowerCase()
    );
  }, [availableTokens, otherToken]);

  const handleTokenSelect = (token: SwapToken) => {
    onTokenSelect(token);
    setIsOpen(false);
    setSearchQuery('');
  };



  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="w-[140px] justify-between"
          disabled={disabled}
        >
          <div className="flex items-center gap-2">
            {selectedToken.logoURI ? (
              <img
                src={selectedToken.logoURI}
                alt={selectedToken.symbol}
                className="w-5 h-5 rounded-full"
                onError={(e) => {
                  // Fallback to icon if image fails
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling!.style.display = 'inline';
                }}
              />
            ) : null}
            <span
              className={selectedToken.logoURI ? 'hidden' : 'inline'}
            >
              {selectedToken.icon}
            </span>
            <span className="font-medium">{selectedToken.symbol}</span>
          </div>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Select Token</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tokens..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Token List */}
          <div className="max-h-96 overflow-y-auto space-y-1">
            {filteredTokens.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {searchQuery ? 'No tokens found' : 'Loading tokens...'}
              </div>
            ) : (
              filteredTokens.map((token) => (
                <button
                  key={token.address}
                  onClick={() => handleTokenSelect(token)}
                  className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors text-left"
                >
                  {token.logoURI ? (
                    <img
                      src={token.logoURI}
                      alt={token.symbol}
                      className="w-8 h-8 rounded-full"
                      onError={(e) => {
                        // Fallback to icon if image fails
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling!.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div
                    className={`w-8 h-8 rounded-full bg-muted flex items-center justify-center text-lg ${
                      token.logoURI ? 'hidden' : 'flex'
                    }`}
                  >
                    {token.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{token.symbol}</div>
                    <div className="text-sm text-muted-foreground truncate">
                      {token.name}
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TokenSelector;
