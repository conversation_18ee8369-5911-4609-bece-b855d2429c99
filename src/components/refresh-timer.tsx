import { useEffect, useState } from 'react';

interface RefreshTimerProps {
  size?: number;
  onRefresh?: () => void;
}

const RefreshTimer = ({ size = 6, onRefresh }: RefreshTimerProps) => {
  // Interval cố định 30 giây
  const REFRESH_INTERVAL = 10000;

  // State để theo dõi thời gian còn lại
  const [timeLeft, setTimeLeft] = useState(REFRESH_INTERVAL);

  useEffect(() => {
    // Setup timer để đếm ngược
    const timer = setInterval(() => {
      setTimeLeft(prevTime => {
        // Nếu thời gian đã hết, reset lại và tăng refreshCount
        if (prevTime <= 1000) {
          onRefresh?.();
          return REFRESH_INTERVAL;
        }
        return prevTime - 1000;
      });
    }, 1000);

    // Cleanup interval khi component unmount
    return () => clearInterval(timer);
  }, [onRefresh]);

  const percentage = (timeLeft / REFRESH_INTERVAL) * 100;

  // Tính toán tham số cho SVG
  const radius = 45;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference * (1 - percentage / 100);

  const handleRefresh = () => {
    onRefresh?.();
    setTimeLeft(REFRESH_INTERVAL);
  };

  return (
    <svg className={`size-${size} cursor-pointer`} viewBox="0 0 100 100" onClick={handleRefresh}>
      <circle cx="50" cy="50" r={radius} strokeWidth="10" stroke="#d3d3d3" fill="none" />
      {/* Vòng tròn tiến trình với animation */}
      <circle
        cx="50"
        cy="50"
        r={radius}
        stroke="#088b88"
        strokeWidth="10"
        fill="none"
        strokeLinecap="round"
        strokeDasharray={circumference}
        strokeDashoffset={strokeDashoffset}
        transform="rotate(-90 50 50)"
        className="transition-all duration-1000 ease-linear"
      />
    </svg>
  );
};

export default RefreshTimer;
