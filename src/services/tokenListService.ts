import tokenList from './hyperswap-token-list/tokens.json';

export interface TokenListToken {
  name: string;
  decimals: number;
  symbol: string;
  address: string;
  chainId: number;
  logoURI: string;
  tags?: string[];
}

export interface TokenList {
  name: string;
  logoURI: string;
  keywords: string[];
  tags: Record<string, { name: string; description: string }>;
  tokens: TokenListToken[];
  timestamp: string;
  version: {
    major: number;
    minor: number;
    patch: number;
  };
}

// Load token list
export const getTokenList = (): TokenList => {
  return tokenList as TokenList;
};

// Get tokens for specific chain
export const getTokensForChain = (chainId: number = 999): TokenListToken[] => {
  const list = getTokenList();
  return list.tokens.filter(token => token.chainId === chainId);
};

// Get token by address
export const getTokenByAddress = (address: string, chainId: number = 999): TokenListToken | undefined => {
  const tokens = getTokensForChain(chainId);
  return tokens.find(token => token.address.toLowerCase() === address.toLowerCase());
};

// Get token by symbol
export const getTokenBySymbol = (symbol: string, chainId: number = 999): TokenListToken | undefined => {
  const tokens = getTokensForChain(chainId);
  return tokens.find(token => token.symbol.toLowerCase() === symbol.toLowerCase());
};

// Get popular/featured tokens for swap
export const getFeaturedTokens = (chainId: number = 999): TokenListToken[] => {
  const tokens = getTokensForChain(chainId);
  
  // Define featured token symbols (most popular ones)
  const featuredSymbols = [
    'WETH', 'USDC', 'WHYPE', 'USDe', 'HFUN', 'OMNIX', 'PURR', 'JEFF', 'HYPE',
    'BASED', 'DEGEN', 'HIGHER', 'MFER', 'TOSHI', 'BRETT', 'PEPE', 'WIF'
  ];
  
  const featured: TokenListToken[] = [];
  
  // Add featured tokens in order
  featuredSymbols.forEach(symbol => {
    const token = getTokenBySymbol(symbol, chainId);
    if (token) {
      featured.push(token);
    }
  });
  
  // Add remaining tokens (up to 50 total)
  const remaining = tokens.filter(token => 
    !featuredSymbols.includes(token.symbol) && 
    featured.length < 50
  ).slice(0, 50 - featured.length);
  
  return [...featured, ...remaining];
};

// Search tokens
export const searchTokens = (query: string, chainId: number = 999): TokenListToken[] => {
  if (!query || query.length < 2) return [];
  
  const tokens = getTokensForChain(chainId);
  const lowerQuery = query.toLowerCase();
  
  return tokens.filter(token => 
    token.symbol.toLowerCase().includes(lowerQuery) ||
    token.name.toLowerCase().includes(lowerQuery) ||
    token.address.toLowerCase().includes(lowerQuery)
  ).slice(0, 20); // Limit to 20 results
};

// Validate if token exists in list
export const isTokenInList = (address: string, chainId: number = 999): boolean => {
  return !!getTokenByAddress(address, chainId);
};

// Get token logo URL with fallback
export const getTokenLogoUrl = (address: string, chainId: number = 999): string => {
  const token = getTokenByAddress(address, chainId);
  if (token?.logoURI) {
    return token.logoURI;
  }
  
  // Fallback to generic token icon
  return `https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/${address}/logo.png`;
};
