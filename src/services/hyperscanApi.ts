import { HyperScanNftCollectionsResponse, HyperScanNftNextPageParams, HyperScanNFTResponse } from "@/types/hyperEvm";
import { ENDPOINTS } from "./endpoints";

export const fetchHyperEvmERC20Tokens = async (address: string) => {
    const response = await fetch(
        `${ENDPOINTS.HYPEREVM_MAINNET}/addresses/${address}/tokens?type=ERC-20`
    );

    if (!response.ok) {
        throw new Error(`Network response was not ok: ${response.status}`);
    }

    return await response.json();
};


export const fetchHyperEvmNfts = async (address: string, nextPageParams?: HyperScanNftNextPageParams): Promise<HyperScanNFTResponse> => {
    let nextPageParamsString = '';
    if (nextPageParams) {
        nextPageParamsString = `items_count=${nextPageParams?.items_count}&token_contract_address_hash=${nextPageParams?.token_contract_address_hash}&token_id=${nextPageParams?.token_id}&token_type=${nextPageParams?.token_type}`;
    }
    const response = await fetch(
        `${ENDPOINTS.HYPEREVM_MAINNET}/addresses/${address}/nft?${nextPageParamsString}`
    );

    if (!response.ok) {
        throw new Error(`Network response was not ok: ${response.status}`);
    }

    return await response.json();
};

export const fetchHyperEvmNftsCollection = async (address: string): Promise<HyperScanNftCollectionsResponse> => {
    const response = await fetch(
        `${ENDPOINTS.HYPEREVM_MAINNET}/addresses/${address}/nft/collections`
    );

    if (!response.ok) {
        throw new Error(`Network response was not ok: ${response.status}`);
    }

    return await response.json();
};