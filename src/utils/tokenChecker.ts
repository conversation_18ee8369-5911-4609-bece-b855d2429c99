import { createHyperSwapClients, checkTokenSupport, checkTradingPairSupport } from '@/lib/swap/v3-swap-functions';
import { HYPERSWAP_CONSTANTS } from '@/constants/hyperswap';

// Known supported tokens on HyperSwap V3
export const KNOWN_SUPPORTED_TOKENS = {
  WETH: '******************************************',
  USDC: '******************************************',
  // Add more as you discover them
} as const;

// Check if a token address is supported
export const isTokenSupported = async (tokenAddress: string): Promise<{
  isSupported: boolean;
  tokenInfo?: {
    symbol: string;
    decimals: number;
    name: string;
  };
  error?: string;
}> => {
  try {
    const { publicClient } = createHyperSwapClients();
    const result = await checkTokenSupport(publicClient, tokenAddress as `0x${string}`);
    
    if (result.isSupported) {
      return {
        isSupported: true,
        tokenInfo: {
          symbol: result.symbol!,
          decimals: result.decimals!,
          name: result.name!,
        },
      };
    } else {
      return {
        isSupported: false,
        error: 'Token is not a valid ERC20 token or not deployed on this network',
      };
    }
  } catch (error) {
    return {
      isSupported: false,
      error: `Error checking token: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
};

// Check if a trading pair is supported
export const isTradingPairSupported = async (
  tokenInAddress: string,
  tokenOutAddress: string
): Promise<{
  isSupported: boolean;
  error?: string;
}> => {
  try {
    const isSupported = await checkTradingPairSupport(tokenInAddress, tokenOutAddress);
    
    if (isSupported) {
      return { isSupported: true };
    } else {
      return {
        isSupported: false,
        error: 'Trading pair not found or no liquidity available',
      };
    }
  } catch (error) {
    return {
      isSupported: false,
      error: `Error checking trading pair: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
};

// Get all supported tokens (this would need to be expanded with actual discovery logic)
export const getSupportedTokens = async (): Promise<Array<{
  address: string;
  symbol: string;
  decimals: number;
  name: string;
}>> => {
  const supportedTokens = [];
  
  for (const [symbol, address] of Object.entries(KNOWN_SUPPORTED_TOKENS)) {
    try {
      const result = await isTokenSupported(address);
      if (result.isSupported && result.tokenInfo) {
        supportedTokens.push({
          address,
          ...result.tokenInfo,
        });
      }
    } catch (error) {
      console.error(`Error checking ${symbol}:`, error);
    }
  }
  
  return supportedTokens;
};

// Validate swap parameters before execution
export const validateSwapParams = async (
  tokenInAddress: string,
  tokenOutAddress: string,
  amount: string
): Promise<{
  isValid: boolean;
  errors: string[];
}> => {
  const errors: string[] = [];
  
  // Check if amount is valid
  if (!amount || parseFloat(amount) <= 0) {
    errors.push('Amount must be greater than 0');
  }
  
  // Check if tokens are the same
  if (tokenInAddress.toLowerCase() === tokenOutAddress.toLowerCase()) {
    errors.push('Cannot swap token with itself');
  }
  
  // Check if tokens are supported
  const [tokenInCheck, tokenOutCheck] = await Promise.all([
    isTokenSupported(tokenInAddress),
    isTokenSupported(tokenOutAddress),
  ]);
  
  if (!tokenInCheck.isSupported) {
    errors.push(`Input token is not supported: ${tokenInCheck.error}`);
  }
  
  if (!tokenOutCheck.isSupported) {
    errors.push(`Output token is not supported: ${tokenOutCheck.error}`);
  }
  
  // Check if trading pair is supported
  if (tokenInCheck.isSupported && tokenOutCheck.isSupported) {
    const pairCheck = await isTradingPairSupported(tokenInAddress, tokenOutAddress);
    if (!pairCheck.isSupported) {
      errors.push(`Trading pair is not supported: ${pairCheck.error}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};
