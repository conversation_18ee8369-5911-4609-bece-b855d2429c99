// Mapping of coin symbols to their icon URLs
export const coinIcons: Record<string, string> = {
  // Major cryptocurrencies
  BTC: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
  ETH: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
  USDC: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
  USDT: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
  SOL: 'https://cryptologos.cc/logos/solana-sol-logo.png',
  BNB: 'https://cryptologos.cc/logos/bnb-bnb-logo.png',
  XRP: 'https://cryptologos.cc/logos/xrp-xrp-logo.png',
  ADA: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
  AVAX: 'https://cryptologos.cc/logos/avalanche-avax-logo.png',
  DOGE: 'https://cryptologos.cc/logos/dogecoin-doge-logo.png',
  DOT: 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png',
  MATIC: 'https://cryptologos.cc/logos/polygon-matic-logo.png',
  LINK: 'https://cryptologos.cc/logos/chainlink-link-logo.png',
  UNI: 'https://cryptologos.cc/logos/uniswap-uni-logo.png',
  AAVE: 'https://cryptologos.cc/logos/aave-aave-logo.png',
  ATOM: 'https://cryptologos.cc/logos/cosmos-atom-logo.png',
  LTC: 'https://cryptologos.cc/logos/litecoin-ltc-logo.png',
  BCH: 'https://cryptologos.cc/logos/bitcoin-cash-bch-logo.png',
  ALGO: 'https://cryptologos.cc/logos/algorand-algo-logo.png',
  FIL: 'https://cryptologos.cc/logos/filecoin-fil-logo.png',
  XLM: 'https://cryptologos.cc/logos/stellar-xlm-logo.png',
  EOS: 'https://cryptologos.cc/logos/eos-eos-logo.png',
  NEAR: 'https://cryptologos.cc/logos/near-protocol-near-logo.png',
  APE: 'https://cryptologos.cc/logos/apecoin-ape-logo.png',
  ARB: 'https://cryptologos.cc/logos/arbitrum-arb-logo.png',
  OP: 'https://cryptologos.cc/logos/optimism-op-logo.png',
  HYPE: 'https://i.postimg.cc/WtRJRmq4/hyperliquid.png',
};

// Function to get coin icon URL
export const getCoinIconUrl = (symbol: string): string => {
  const defaultIcon = 'https://cryptologos.cc/logos/question-mark.png';
  return coinIcons[symbol] || defaultIcon;
};
