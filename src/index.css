@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans:
    'Figtree', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
    Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

:root {
  --radius: 1rem;
  --background: #f7f7f9;
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: #088b88;
  --primary-foreground: oklch(0.982 0.018 155.826);
  --secondary: oklch(0.734 0.095 183.797);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.558 0.124 194.797);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.558 0.124 194.797);
  --sidebar-primary-foreground: oklch(0.982 0.018 155.826);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.558 0.124 194.797);
  --gradient-background: linear-gradient(in oklch 135deg,
      oklch(0.97 0.01 196),
      oklch(0.95 0.02 196),
      oklch(0.97 0.01 196));
}

.dark {
  --background: #0c0e13;
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: #088b88;
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.734 0.095 183.797);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.558 0.124 194.797);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.558 0.124 194.797);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.558 0.124 194.797);
  --gradient-background: linear-gradient(in oklch 135deg,
      oklch(0.1 0.01 0),
      oklch(0.15 0.05 196),
      oklch(0.1 0.01 0));
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
  }

  /* Prevent zooming on input focus */
  input,
  textarea,
  select {
    font-size: 16px;
    /* Prevents iOS zoom on focus */
    touch-action: manipulation;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans);
    animation: fadein 0.5s;
    overscroll-behavior-y: auto;
  }

  html,
  body {
    touch-action: auto;
    overflow: visible;
    position: relative;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    -webkit-overflow-scrolling: touch;
  }

  main {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    overflow-x: hidden;
    overscroll-behavior-y: auto;
  }

  /* Safe area utilities for iOS devices */
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pt-safe {
    padding-top: env(safe-area-inset-top);
  }
}

.dynamic-shadow-dom {
  --dynamic-footer-padding: 0.75rem 1.5rem 2rem;
  --dynamic-font-family-primary: var(--font-sans);
}

@utility container {
  max-width: 100rem;
}

/* Hide scrollbar for all browsers */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--primary);
  opacity: 0.7;
  border-radius: var(--radius-md);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  opacity: 1;
}

/* Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--primary) transparent;
}

/* Recharts */
/* CSS cho thẻ cha chứa AreaChart */
.chart-container {
  width: 100%;
  height: 60vh;
  /* hoặc chiều cao cụ thể như 400px */
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border, #e2e8f0);
  border-radius: 8px;
  background: var(--background, #ffffff);
}

/* CSS cho ChartContainer component */
.chart-container .recharts-wrapper {
  width: 100% !important;
  height: 100% !important;
}

/* CSS cho responsive chart */
.chart-container .recharts-surface {
  width: 100% !important;
  height: 100% !important;
}

/* CSS cho AreaChart cụ thể */
.area-chart-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Đảm bảo chart fill toàn bộ container */
.area-chart-wrapper .recharts-responsive-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 300px;
  /* chiều cao tối thiểu */
}

/* CSS cho tooltip */
.chart-tooltip {
  background: var(--popover, #ffffff);
  border: 1px solid var(--border, #e2e8f0);
  border-radius: 6px;
  padding: 8px 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* CSS cho grid lines */
.chart-container .recharts-cartesian-grid-horizontal line,
.chart-container .recharts-cartesian-grid-vertical line {
  stroke: var(--muted, #f1f5f9);
  stroke-width: 1;
  opacity: 0.5;
}

/* CSS cho area fill gradient (tùy chọn) */
.chart-container .recharts-area {
  fill: url(#areaGradient);
}

/* Định nghĩa gradient cho area */
.area-gradient {
  --gradient-from: rgba(59, 130, 246, 0.1);
  --gradient-to: rgba(59, 130, 246, 0);
}

/* CSS cho mobile responsive */
@media (max-width: 768px) {
  .chart-container {
    height: 50vh;
    min-height: 300px;
  }

  .chart-container .recharts-wrapper {
    font-size: 12px;
  }
}

/* CSS cho dark mode (nếu cần) */
@media (prefers-color-scheme: dark) {
  .chart-container {
    background: var(--background-dark, #1f2937);
    border-color: var(--border-dark, #374151);
  }

  .chart-tooltip {
    background: var(--popover-dark, #1f2937);
    border-color: var(--border-dark, #374151);
    color: var(--foreground-dark, #f9fafb);
  }
}

/* CSS cho loading state */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--muted-foreground, #64748b);
}

/* CSS cho empty state */
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--muted-foreground, #64748b);
  text-align: center;
}

/* Tailwind CSS classes tương ứng */
.chart-container-tw {
  @apply w-full h-[60vh] relative overflow-hidden border border-border rounded-lg bg-background;
}

.chart-wrapper-tw {
  @apply w-full h-full flex items-center justify-center;
}

.chart-loading-tw {
  @apply flex items-center justify-center h-full text-muted-foreground;
}

.chart-empty-tw {
  @apply flex flex-col items-center justify-center h-full text-muted-foreground text-center;
}

@keyframes progress {
  0% {
    width: 0%;
    margin-left: 0%;
  }

  50% {
    width: 30%;
    margin-left: 70%;
  }

  100% {
    width: 0%;
    margin-left: 0%;
  }
}

@keyframes fadein {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}