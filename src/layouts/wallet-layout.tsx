import { useRef, useState, useEffect } from 'react';
import WalletController from '@/routes/wallet/wallet-controller';
import { Outlet } from 'react-router';
import useWalletTabStore, { WalletTab } from '@/store/walletTab';

const WalletLayout = () => {
    const { currentTab, setCurrentTab } = useWalletTabStore();
    const [touchStart, setTouchStart] = useState(0);
    const [touchEnd, setTouchEnd] = useState(0);
    const [swipeProgress, setSwipeProgress] = useState(0);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const tabsRef = useRef(['spot', 'perps', 'evm', 'nft']);

    // Theo dõi xem người dùng đã từng thực hiện vuốt chưa
    const [hasSwipedBefore, setHasSwipedBefore] = useState(() => {
        return localStorage.getItem('hasSwipedBefore') === 'true';
    });

    // Lưu trạng thái đã vuốt
    useEffect(() => {
        if (swipeProgress !== 0 && !hasSwipedBefore) {
            setHasSwipedBefore(true);
            localStorage.setItem('hasSwipedBefore', 'true');
        }
    }, [swipeProgress, hasSwipedBefore]);

    const handleTabChange = (tab: WalletTab) => {
        if (currentTab !== tab) {
            setIsTransitioning(true);
            setCurrentTab(tab);
            setTimeout(() => {
                setIsTransitioning(false);
                setSwipeProgress(0);
            }, 300);
        }
    };

    const handleTouchStart = (e: React.TouchEvent) => {
        if (isTransitioning) return;
        setTouchStart(e.targetTouches[0].clientX);
    };

    const handleTouchMove = (e: React.TouchEvent) => {
        if (isTransitioning || !touchStart) return;
        const currentTouch = e.targetTouches[0].clientX;
        setTouchEnd(currentTouch);

        if (touchStart) {
            const delta = touchStart - currentTouch;
            const maxSwipe = 100; // Khoảng cách tối đa để tính là vuốt hoàn toàn
            const progress = Math.max(-1, Math.min(1, delta / maxSwipe));
            setSwipeProgress(progress);
        }
    };

    const handleTouchEnd = () => {
        if (isTransitioning || !touchStart || !touchEnd) return;

        const distance = touchStart - touchEnd;
        const isLeftSwipe = distance > 50;
        const isRightSwipe = distance < -50;

        const tabs = tabsRef.current;
        const currentIndex = tabs.indexOf(currentTab);

        // Reset touch values
        setTouchStart(0);
        setTouchEnd(0);

        if (isLeftSwipe && currentIndex < tabs.length - 1) {
            handleTabChange(tabs[currentIndex + 1] as WalletTab);
        } else if (isRightSwipe && currentIndex > 0) {
            handleTabChange(tabs[currentIndex - 1] as WalletTab);
        } else {
            setSwipeProgress(0);
        }
    };

    return (
        <>
            <WalletController />
            <div className="relative mt-2">
                <div className="absolute top-0 right-0 left-0">
                    <div className="bg-background border-border sticky top-0 z-10 mb-2 flex border-b transition-all duration-300">
                        {tabsRef.current.map((tab) => (
                            <button
                                key={tab}
                                className={`px-4 py-2 text-base font-medium ${currentTab === tab
                                    ? 'text-primary border-primary border-b-2'
                                    : 'text-muted-foreground hover:text-foreground'
                                    }`}
                                onClick={() => handleTabChange(tab as WalletTab)}
                            >
                                {tab === 'spot' ? 'Spot' :
                                    tab === 'perps' ? 'Perpetuals' :
                                        tab === 'evm' ? 'EVM' : 'NFTs'}
                            </button>
                        ))}
                    </div>

                    {/* Swipe tutorial - hiển thị chỉ khi chưa từng vuốt */}
                    {!hasSwipedBefore && (
                        <div
                            className="md:hidden bg-background border border-border rounded-md mx-4 mb-4 p-3 flex items-center justify-center text-sm"
                        >
                            <svg className="w-4 h-4 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                            </svg>
                            <span>Swipe left or right to switch tabs</span>
                        </div>
                    )}

                    {/* Tab Content */}
                    <div
                        onTouchStart={handleTouchStart}
                        onTouchMove={handleTouchMove}
                        onTouchEnd={handleTouchEnd}
                        className='pb-28'
                    >
                        <Outlet />
                    </div>
                </div>
            </div>
        </>
    );
};

export default WalletLayout;