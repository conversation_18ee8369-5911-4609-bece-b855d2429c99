import LoginHeader from '@/routes/login/login-header';
import { useIsLoggedIn } from '@dynamic-labs/sdk-react-core';
import { Navigate, Outlet } from 'react-router';

const LoginLayout = () => {
  const isLoggedIn = useIsLoggedIn();
  return (
    <main className="bg-background">
      <div className="mx-auto w-full max-w-6xl space-y-20 md:p-6">
        <LoginHeader />
        {isLoggedIn ? <Navigate to="/" /> : <Outlet />}
      </div>
    </main>
  );
};

export default LoginLayout;
