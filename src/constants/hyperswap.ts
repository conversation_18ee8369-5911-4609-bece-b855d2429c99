// HyperSwap V3 Constants
export const HYPERSWAP_CONSTANTS = {
  // RPC URLs - Use consistent URL from env
  RPC_URL: 'https://rpc.hyperliquid-testnet.xyz/evm',

  // Contract Addresses
  ROUTER_V3_ADDRESS: '******************************************' as `0x${string}`,
  WETH_ADDRESS: '******************************************' as `0x${string}`,
  USDC_ADDRESS: '******************************************' as `0x${string}`,

  // Chain Configuration
  HYPER_CHAIN: {
    id: 999,
    name: 'Hyperliquid EVM',
    nativeCurrency: {
      name: '<PERSON><PERSON><PERSON>',
      symbol: 'HYPE',
      decimals: 18
    },
    rpcUrls: {
      default: {
        http: ['https://rpc.hyperliquid-testnet.xyz/evm']
      }
    }
  }
} as const;

// Token Configuration
export interface SwapToken {
  symbol: string;
  name: string;
  address: `0x${string}`;
  decimals: number;
  icon?: string;
  logoURI?: string;
}

// Import from token list service
import { getFeaturedTokens } from '@/services/tokenListService';

// Get featured tokens for swap
export const getSwapTokens = (): SwapToken[] => {
  const featuredTokens = getFeaturedTokens(999);

  return featuredTokens.map(token => ({
    symbol: token.symbol,
    name: token.name,
    address: token.address as `0x${string}`,
    decimals: token.decimals,
    logoURI: token.logoURI,
    icon: getTokenIcon(token.symbol), // Fallback icon
  }));
};

// Get icon for token symbol (fallback)
const getTokenIcon = (symbol: string): string => {
  const iconMap: Record<string, string> = {
    'WETH': 'Ξ',
    'ETH': 'Ξ',
    'USDC': '💵',
    'USDT': '💵',
    'USDe': '💵',
    'WHYPE': '🔥',
    'HYPE': '🔥',
    'HFUN': '🎮',
    'OMNIX': '🌐',
    'PURR': '🐱',
    'JEFF': '🤖',
    'BASED': '🔵',
    'DEGEN': '🎩',
    'HIGHER': '⬆️',
    'MFER': '😎',
    'TOSHI': '🐕',
    'BRETT': '🐸',
    'PEPE': '🐸',
    'WIF': '🐕',
  };

  return iconMap[symbol] || '🪙';
};

// Legacy constant for backward compatibility
export const SWAP_TOKENS: SwapToken[] = [
  {
    symbol: 'WETH',
    name: 'Wrapped Ethereum',
    address: HYPERSWAP_CONSTANTS.WETH_ADDRESS,
    decimals: 18,
    icon: 'Ξ',
    logoURI: 'https://assets.coingecko.com/coins/images/2518/standard/weth.png'
  },
  {
    symbol: 'USDC',
    name: 'USD Coin',
    address: HYPERSWAP_CONSTANTS.USDC_ADDRESS,
    decimals: 6,
    icon: '💵',
    logoURI: 'https://assets.coingecko.com/coins/images/6319/standard/usdc.png'
  }
];

// Swap Parameters Interface
export interface SwapParams {
  tokenIn: `0x${string}`;
  tokenOut: `0x${string}`;
  recipient: `0x${string}`;
  amountIn: bigint;
  amountOutMinimum: bigint;
  fee?: number;
  deadline?: bigint;
}
