// HyperSwap V3 Constants
export const HYPERSWAP_CONSTANTS = {
  // RPC URLs
  RPC_URL: 'https://api.hyperliquid-testnet.xyz/evm',
  
  // Contract Addresses
  ROUTER_V3_ADDRESS: '******************************************' as `0x${string}`,
  WETH_ADDRESS: '******************************************' as `0x${string}`,
  USDC_ADDRESS: '******************************************' as `0x${string}`,
  
  // Chain Configuration
  HYPER_CHAIN: {
    id: 999,
    name: 'Hyperliquid EVM',
    nativeCurrency: {
      name: 'HYPE',
      symbol: 'HYPE',
      decimals: 18
    },
    rpcUrls: {
      default: {
        http: ['https://api.hyperliquid-testnet.xyz/evm']
      }
    }
  }
} as const;

// Token Configuration
export interface SwapToken {
  symbol: string;
  name: string;
  address: `0x${string}`;
  decimals: number;
  icon: string;
}

export const SWAP_TOKENS: SwapToken[] = [
  {
    symbol: 'WETH',
    name: 'Wrapped Ethereum',
    address: HYPERSWAP_CONSTANTS.WETH_ADDRESS,
    decimals: 18,
    icon: 'Ξ'
  },
  {
    symbol: 'USDC',
    name: 'USD Coin',
    address: HYPERSWAP_CONSTANTS.USDC_ADDRESS,
    decimals: 6,
    icon: '💵'
  }
];

// Swap Parameters Interface
export interface SwapParams {
  tokenIn: `0x${string}`;
  tokenOut: `0x${string}`;
  recipient: `0x${string}`;
  amountIn: bigint;
  amountOutMinimum: bigint;
  fee?: number;
  deadline?: bigint;
}
