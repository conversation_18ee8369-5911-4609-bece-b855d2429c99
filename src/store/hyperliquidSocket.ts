// useHyperliquidStore.ts
import { create } from 'zustand';
import {
    Network,
    AllMidsData,
    WebSocketMessage,
    AllMidsMessage
} from '@/types/hyperliquidSocket';

// WebSocket URLs
const WS_URLS: Record<Network, string> = {
    mainnet: 'wss://api.hyperliquid.xyz/ws',
    testnet: 'wss://api.hyperliquid-testnet.xyz/ws'
};

// Connection status
export type ConnectionStatus = 'connected' | 'connecting' | 'disconnected' | 'error';

// Đ<PERSON>nh nghĩa trạng thái store
interface HyperliquidState {
    // Connection state
    status: ConnectionStatus;
    error: string | null;
    network: Network;
    ws: WebSocket | null;
    reconnectAttempts: number;
    reconnectTimer: NodeJS.Timeout | null;

    // Data
    allMids: AllMidsData;

    // Config
    reconnectInterval: number;
    maxReconnectAttempts: number;

    // Actions
    setNetwork: (network: Network) => void;
    connect: () => void;
    disconnect: () => void;
    subscribeToAllMids: () => void;
    unsubscribeFromAllMids: () => void;

    // Setup function (được gọi một lần khi khởi tạo)
    setupWebSocketHandlers: (ws: WebSocket) => void;
}

// Tạo store
const useHyperliquidSocketStore = create<HyperliquidState>((set, get) => ({
    // Initial state
    status: 'disconnected',
    error: null,
    network: 'mainnet',
    ws: null,
    reconnectAttempts: 0,
    reconnectTimer: null,
    allMids: { mids: {} },
    reconnectInterval: 5000,
    maxReconnectAttempts: 5,

    // Đặt network
    setNetwork: (network) => set({ network }),

    // Kết nối tới WebSocket
    connect: () => {
        // Lấy state hiện tại
        const {
            network,
            ws: currentWs,
            setupWebSocketHandlers
        } = get();

        try {
            // Đặt trạng thái connecting
            set({ status: 'connecting', error: null });

            // Đóng kết nối hiện tại nếu có
            if (currentWs && currentWs.readyState !== WebSocket.CLOSED) {
                currentWs.close();
            }

            // Tạo WebSocket mới
            const wsUrl = WS_URLS[network];
            if (!wsUrl) {
                throw new Error(`Network không hợp lệ: ${network}`);
            }

            const ws = new WebSocket(wsUrl);

            // Lưu trữ đối tượng WebSocket
            set({ ws });

            // Setup handlers
            setupWebSocketHandlers(ws);

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Không thể kết nối';
            console.error('Failed to establish WebSocket connection:', errorMessage);
            set({
                status: 'error',
                error: errorMessage,
                ws: null
            });
        }
    },

    // Ngắt kết nối WebSocket
    disconnect: () => {
        const {
            ws,
            reconnectTimer,
            unsubscribeFromAllMids
        } = get();

        // Xóa timer
        if (reconnectTimer) {
            clearTimeout(reconnectTimer);
            set({ reconnectTimer: null });
        }

        if (ws) {
            // Hủy subscriptions
            if (ws.readyState === WebSocket.OPEN) {
                unsubscribeFromAllMids();
            }

            // Đóng kết nối
            ws.close(1000, 'Closed by client');

            // Cập nhật state
            set({
                ws: null,
                status: 'disconnected',
                error: null
            });
        }
    },

    // Subscribe to allMids
    subscribeToAllMids: () => {
        const { ws } = get();

        if (ws && ws.readyState === WebSocket.OPEN) {
            const message = {
                method: 'subscribe',
                subscription: { type: 'allMids' }
            };
            ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket chưa sẵn sàng để subscribe');
        }
    },

    // Unsubscribe from allMids
    unsubscribeFromAllMids: () => {
        const { ws } = get();

        if (ws && ws.readyState === WebSocket.OPEN) {
            const message = {
                method: 'unsubscribe',
                subscription: { type: 'allMids' }
            };
            ws.send(JSON.stringify(message));
        }
    },

    // Setup WebSocket handlers
    setupWebSocketHandlers: (ws) => {
        // Lấy state và actions
        const {
            reconnectInterval,
            maxReconnectAttempts,
            connect,
            subscribeToAllMids
        } = get();

        // onopen handler
        ws.onopen = () => {


            // Cập nhật state
            set({
                status: 'connected',
                error: null,
                reconnectAttempts: 0
            });

            // Tự động subscribe to allMids
            subscribeToAllMids();
        };

        // onclose handler
        ws.onclose = (event) => {

            // Cập nhật state
            set({ status: 'disconnected' });

            // Thử kết nối lại nếu không đóng thủ công
            const { reconnectAttempts } = get();

            if (!event.wasClean && reconnectAttempts < maxReconnectAttempts) {
                const timer = setTimeout(() => {
                    // Tăng số lần thử kết nối
                    set({ reconnectAttempts: reconnectAttempts + 1 });



                    // Kết nối lại
                    connect();
                }, reconnectInterval);

                // Lưu timer
                set({ reconnectTimer: timer });
            }
        };

        // onerror handler
        ws.onerror = () => {

            // Cập nhật state
            set({
                status: 'error',
                error: 'Lỗi kết nối WebSocket'
            });
        };

        // onmessage handler
        ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data) as WebSocketMessage;

                // Xử lý dữ liệu từ allMids
                if (message.channel === 'allMids') {
                    const allMidsMessage = message as AllMidsMessage;

                    // Cập nhật state với dữ liệu mới
                    set({ allMids: allMidsMessage.data });
                }
                // // Xử lý phản hồi từ subscription
                // else if (message.channel === 'subscriptionResponse') {

                // }
            } catch {
                // const error = err instanceof Error ? err : new Error('Unknown error');
            }
        };
    }
}));

export default useHyperliquidSocketStore;