import { create } from "zustand";
import { persist, PersistOptions } from "zustand/middleware";

export type MarketHyperEvmTab = "trending" | "hyperswap-v3" | "hyperswap-v2" | "laminar" | 'kittenswap';

interface State {
    currentTab: MarketHyperEvmTab;
}

interface Action {
    setCurrentTab: (tab: MarketHyperEvmTab) => void;
}

type MarketHyperEvmTabState = State & Action;

const persistOptions: PersistOptions<MarketHyperEvmTabState, Pick<State, 'currentTab'>> = {
    name: "market-hyperevm-tab-storage",
    partialize: (state) => ({ currentTab: state.currentTab }),
};

const useMarketHyperEvmTabStore = create<MarketHyperEvmTabState>()(
    persist(
        (set) => ({
            currentTab: "trending",
            setCurrentTab: (tab) => set({ currentTab: tab })
        }),
        persistOptions
    )
);

export default useMarketHyperEvmTabStore;