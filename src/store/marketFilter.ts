import { TimeFrame } from '@/types/geckoTerminal';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface MarketFilterState {
  timeFrame: TimeFrame;
}

interface MarketFilterProps {
  setTimeFrame: (timeFrame: TimeFrame) => void;
}

const useMarketFilter = create<MarketFilterProps & MarketFilterState>()(
  persist(
    set => ({
      timeFrame: 'h24',
      setTimeFrame: timeFrame => set({ timeFrame }),
    }),
    {
      name: 'market-filter-storage',
      partialize: state => ({ timeFrame: state.timeFrame }),
    }
  )
);

export default useMarketFilter;
