{"name": "purro-app", "private": true, "version": "0.7.21", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate-pwa-assets": "pwa-assets-generator", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@dynamic-labs/bitcoin": "^4.18.3", "@dynamic-labs/ethereum": "^4.18.3", "@dynamic-labs/global-wallet": "^4.18.3", "@dynamic-labs/sdk-api": "^0.0.666", "@dynamic-labs/sdk-react-core": "^4.18.3", "@dynamic-labs/solana": "^4.18.3", "@dynamic-labs/sui": "^4.18.3", "@dynamic-labs/waas-evm": "^4.18.3", "@dynamic-labs/waas-sui": "^4.18.3", "@dynamic-labs/waas-svm": "^4.18.3", "@dynamic-labs/wagmi-connector": "^4.18.3", "@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.2", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.75.2", "@tanstack/react-table": "^8.21.3", "@types/lodash": "^4.17.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-react": "^0.507.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-responsive": "^10.0.1", "react-router": "^7.5.3", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.5", "vaul": "^1.1.2", "viem": "^2.29.2", "wagmi": "^2.15.2", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/node": "^22.15.3", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@types/serviceworker": "^0.0.135", "@vite-pwa/assets-generator": "^1.0.0", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tw-animate-css": "^1.2.9", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-pwa": "^1.0.0", "workbox-core": "^7.3.0", "workbox-precaching": "^7.3.0"}}