# HyperSwap V3 Integration Setup

## Overview
Đã setup thành công swap functionality cho USDC/WETH trên Hyperliquid testnet sử dụng template từ HyperSwapX.

## Files đã tạo/chỉnh sửa:

### 1. Constants
- `src/constants/hyperswap.ts` - Chứa addresses, chain config và token definitions

### 2. Swap Libraries
- `src/lib/swap/v3-swap-functions.ts` - Core swap functions sử dụng viem
- `src/lib/swap/abi/router-v3.ts` - ABI cho HyperSwap V3 Router
- `src/lib/swap/erc20-abi.ts` - Standard ERC20 ABI

### 3. React Hooks
- `src/hooks/useSwap.ts` - Hook quản lý swap state và wallet integration

### 4. UI Components
- `src/routes/trade/components/swap-box.tsx` - Updated với real swap functionality

## Environment Variables
Đã có sẵn trong .env:
```
RPC_URL=https://api.hyperliquid-testnet.xyz/evm
ROUTER_V3_ADDRESS=******************************************
WETH_ADDRESS=******************************************
USDC_ADDRESS=******************************************
```

## Features đã implement:

### ✅ Wallet Connection
- Tích hợp với Dynamic wallet
- Hiển thị trạng thái connection
- Button connect wallet khi chưa connect

### ✅ Token Selection
- Dropdown chọn WETH/USDC
- Hiển thị icon và symbol
- Swap tokens với button arrow

### ✅ Balance Display
- Hiển thị balance của từng token
- Real-time update sau khi swap
- Format số đẹp

### ✅ Swap Execution
- Input amount validation
- Slippage protection (0.5% fixed)
- Token approval tự động
- Transaction submission
- Loading states

### ✅ Error Handling
- Hiển thị lỗi rõ ràng
- Validation input
- Network error handling

### ✅ Transaction Tracking
- Link đến explorer khi thành công
- Success/error messages
- Transaction hash display

## Cách sử dụng:

1. **Connect Wallet**: Click "Connect Wallet" để kết nối
2. **Select Tokens**: Chọn token muốn swap (WETH ↔ USDC)
3. **Enter Amount**: Nhập số lượng muốn swap
4. **Execute Swap**: Click "Swap" để thực hiện
5. **Confirm Transaction**: Confirm trong wallet
6. **View Result**: Xem kết quả và link explorer

## Technical Details:

### Swap Process:
1. Check token allowance
2. Approve token if needed
3. Execute exactInputSingle swap
4. Wait for confirmation
5. Update balances

### Security Features:
- Slippage protection
- Deadline protection (30 minutes)
- Amount validation
- Error handling

## Testing:
- Đã test trên Hyperliquid testnet
- Sử dụng testnet tokens
- Integration với Dynamic wallet

## Next Steps:
1. Add price quotes/estimates
2. Add slippage settings
3. Add more token pairs
4. Add swap history
5. Add advanced features (multi-hop swaps)

## Dependencies:
- `viem` - Ethereum client library
- `@dynamic-labs/sdk-react-core` - Wallet connection
- Existing UI components (Button, Input, Select)
