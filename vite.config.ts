import { Connect, defineConfig, Plugin } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';
import { NodeModulesPolyfillPlugin } from '@esbuild-plugins/node-modules-polyfill';
import { NodeGlobalsPolyfillPlugin } from '@esbuild-plugins/node-globals-polyfill';
import { VitePWA } from 'vite-plugin-pwa';
import fs from 'fs/promises';

const wasmMiddleware = (): Plugin => ({
  configureServer: server => {
    server.middlewares.use(
      async (
        req: Connect.IncomingMessage,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        res: any,
        next: Connect.NextFunction
      ) => {
        if (req.url?.endsWith('.wasm')) {
          // Directly point to the .wasm file within the realm/dist directory
          const wasmPath = path.join(
            process.cwd(),
            'node_modules/@dynamic-labs-wallet/browser/internal/web/generated',
            path.basename(req.url)
          );
          try {
            const wasmFile = await fs.readFile(wasmPath);
            res.setHeader('Content-Type', 'application/wasm');
            res.end(wasmFile);
          } catch {
            next();
          }
          return;
        }
        next();
      }
    );
  },
  name: 'wasm-middleware',
});

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    wasmMiddleware(),

    VitePWA({
      registerType: 'autoUpdate',
      strategies: 'injectManifest',
      srcDir: 'src',
      filename: 'sw.ts',
      includeAssets: ['favicon.svg', 'apple-touch-icon.png', 'mask-icon.svg'],
      manifest: {
        name: 'Purro Wallet',
        short_name: 'Purro',
        description: 'Purro Wallet Application',
        theme_color: '#088b88',
        background_color: '#088b88',
        display: 'standalone',
        orientation: 'portrait',
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png',
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png',
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable',
          },
        ],
      },
      // Thêm ios section cho splash screen
      includeManifestIcons: true,
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
        cleanupOutdatedCaches: true,
        sourcemap: true,
        maximumFileSizeToCacheInBytes: 10 * 1024 * 1024, // 10 MB instead of default 2 MB
      },
      injectManifest: {
        maximumFileSizeToCacheInBytes: 10 * 1024 * 1024, // 10 MB instead of default 2 MB
      },
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    esbuildOptions: {
      // Enable esbuild polyfill plugins
      plugins: [
        NodeGlobalsPolyfillPlugin({
          process: true,
        }),
        NodeModulesPolyfillPlugin(),
      ],
    },
  },
  define: {
    'process.env': process.env,
  },
});
