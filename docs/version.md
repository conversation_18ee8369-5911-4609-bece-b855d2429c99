# Semantic Versioning cho React Apps

Semantic Versioning (SemVer) cũng áp dụng cho React apps, nhưng có một số điểm cần lưu ý đặc biệt. Dưới đây là hướng dẫn về cách áp dụng SemVer cho ứng dụng React:

## Áp dụng SemVer cho React Apps

### 1. <PERSON><PERSON><PERSON> trúc cơ bản vẫn giữ nguyên:

```
X.Y.Z (MAJOR.MINOR.PATCH)
```

### 2. Khi nào tăng phiên bản trong ứng dụng React:

#### MAJOR (X): Tăng khi:

- Thay đổi cấu trúc dữ liệu cơ bản
- Thay đổi API của components
- Nâng cấp lên phiên bản React mới có breaking changes
- Thay đổi cách render hoặc lifecycle của components
- Thay đổi lớn về UI/UX làm người dùng phải học lại

#### MINOR (Y): Tăng khi:

- Thêm components mới
- Thêm tính năng mới
- Thêm props mới cho components hiện có
- Nâng cấp phiên bản React không có breaking changes

#### PATCH (Z): Tăng khi:

- Sửa lỗi UI
- Sửa bugs
- Cải thiện hiệu suất
- Thay đổi text hoặc nội dung tĩnh

### 3. Quản lý phiên bản trong package.json

Trong file package.json bạn cần cập nhật phiên bản:

```json
{
  "name": "my-react-app",
  "version": "1.2.3",
  ...
}
```

## Các tình huống đặc biệt trong React apps

### 1. Đối với React Libraries/Components

Nếu bạn đang phát triển một thư viện React:

- Tăng MAJOR khi thay đổi API công khai
- Tăng MINOR khi thêm components hoặc props mới
- Tăng PATCH khi sửa lỗi mà không ảnh hưởng đến API

### 2. Đối với React App hoàn chỉnh:

Đối với ứng dụng React dành cho người dùng cuối:

- Tăng MAJOR khi có thay đổi lớn về UI/UX hoặc workflow
- Tăng MINOR khi thêm tính năng mới
- Tăng PATCH khi sửa lỗi hoặc cải thiện UI nhỏ

### 3. Xử lý dependencies:

Trong package.json, chỉ định dependencies một cách thông minh:

```json
{
  "dependencies": {
    "react": "^18.0.0", // Chấp nhận các bản 18.x.x
    "react-router": "~6.3.0", // Chấp nhận các bản 6.3.x
    "axios": "0.27.2" // Chỉ chấp nhận chính xác phiên bản này
  }
}
```

## Mẹo cho việc phát hành React Apps

- **Tạo Changelog**: Luôn ghi lại những thay đổi trong mỗi phiên bản
- **Git Tags**: Sử dụng git tags để đánh dấu các phiên bản
  ```bash
  git tag -a v1.2.3 -m "Version 1.2.3"
  git push origin v1.2.3
  ```
- **GitHub Releases**: Tạo releases trên GitHub với mô tả chi tiết
- **Deploy plans**: Xác định chiến lược triển khai phù hợp với SemVer
  - PATCH: có thể tự động deploy
  - MINOR: cần kiểm tra nhưng ít rủi ro
  - MAJOR: cần kiểm tra kỹ và có kế hoạch chuyển đổi

## Ví dụ thực tế cho React App

- **0.1.0**: Bản demo đầu tiên có UI cơ bản
- **0.2.0**: Thêm chức năng đăng nhập
- **0.2.1**: Sửa lỗi validation form
- **1.0.0**: Phiên bản đầy đủ đầu tiên cho người dùng
- **1.1.0**: Thêm dark mode
- **2.0.0**: Thiết kế lại toàn bộ UI và nâng cấp lên React 18

Việc áp dụng SemVer cho React apps giúp quản lý phát triển hiệu quả và giúp người dùng/nhà phát triển dễ dàng nắm bắt mức độ thay đổi trong mỗi phiên bản.
